<div class="tb-hover h-full relative bg-transparent data-table-container">
  <div
    class="absolute inset-0 overflow-x-auto overflow-y-auto"
    [ngClass]="{ 'mb-25': !hiddenPaginator }"
  >
    <table class="table-custom w-full bg-transparent border-collapse">
      <thead>
        <tr
          class="bg-transparent border-b border-primary-border dark:border-dark-primary-border"
        >
          @for (column of columns; track $index){
          <th
            class="overflow-auto text-light-text dark:text-dark-text bg-transparent px-3 py-4 lg:px-4 font-medium"
            [style.width]="calcColumnWidth(column)"
            [ngStyle]="{
              'max-width': column.maxWidth,
              'min-width': column.minWidth
            }"
          >
            <ng-container [ngSwitch]="column.columnDef">
              <ng-container *ngSwitchCase="'index'">
                <div class="flex items-center justify-center">
                  <span
                    class="font-bold text-light-text dark:text-dark-text text-sm"
                    >{{ column.headerName }}</span
                  >
                </div>
              </ng-container>

              <ng-container *ngSwitchDefault>
                <div
                  class="flex items-center"
                  [ngStyle]="{ 'justify-content': column.alignHeader }"
                >
                  <span
                    class="font-bold text-light-text dark:text-dark-text text-sm"
                    >{{ column.headerName }}</span
                  >
                </div>
              </ng-container>
            </ng-container>
          </th>
          }
        </tr>
      </thead>
      <tbody>
        @for (row of rows; track $index){
        <tr
          class="bg-transparent hover:bg-light-hover dark:hover:bg-dark-hover transition-colors duration-150 border-b border-primary-border dark:border-dark-primary-border"
          (contextmenu)="onRowRightClick($event, row)"
          >
          <ng-container *ngFor="let column of columns">
            <td
              class="text-light-text dark:text-dark-text bg-transparent py-2.5 px-3 lg:py-3.5 lg:px-4 border-b-0"
            >
              <ng-container [ngSwitch]="column.columnDef">
                <ng-container *ngSwitchCase="'index'">
                  <div class="flex items-center justify-center">
                    <span
                      class="text-light-text/70 dark:text-dark-text/70 text-sm"
                      >{{ getRowIndex(row) + 1 }}</span
                    >
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="'action'">
                  <ng-container
                    [ngTemplateOutlet]="actionTemplate || defaultActions"
                    [ngTemplateOutletContext]="{ $implicit: row }"
                  ></ng-container>
                  <ng-template #defaultActions>
                    <div class="flex justify-center items-center">
                      <button
                        class="flex items-center justify-center"
                        (click)="row.isActions = !row.isActions; row.isContextMenu = false"
                        cdkOverlayOrigin
                        #trigger="cdkOverlayOrigin"
                        dxTooltip="Actions"
                        dxTooltipPosition="above"
                      >
                        <ng-icon
                          name="heroEllipsisHorizontalMini"
                          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content"
                        ></ng-icon>
                      </button>

                      <ng-template
                        cdkConnectedOverlay
                        [cdkConnectedOverlayOrigin]="trigger"
                        [cdkConnectedOverlayOpen]="row.isActions && !row.isContextMenu"
                        [cdkConnectedOverlayPush]="true"
                        [cdkConnectedOverlayPositions]="[
                          {
                            originX: 'start',
                            originY: 'center',
                            overlayX: 'end',
                            overlayY: 'top',
                            offsetX: -5
                          },
                          {
                            originX: 'start',
                            originY: 'center',
                            overlayX: 'end',
                            overlayY: 'bottom',
                            offsetX: -5
                          }
                        ]"
                      >
                        <ul
                          class="w-[245px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 action-dropdown border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                          (clickOutside)="row.isActions = false; row.isContextMenu = false"
                        >
                        <h1>123</h1>
                          <ng-container
                            *ngTemplateOutlet="
                              action;
                              context: { $implicit: row }
                            "
                          ></ng-container>
                        </ul>
                      </ng-template>
                          <!-- overlay context menu tại chuột phải -->
                      <ng-template [ngIf]="row.isActions && row.isContextMenu">
                        <ul
                          class="fixed z-50 w-[246px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 action-dropdown border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                          [style.left.px]="row.contextMenuX"
                          [style.top.px]="row.contextMenuY"
                          (clickOutside)="row.isActions = false; row.isContextMenu = false"
                        >
                          <ng-container
                            *ngTemplateOutlet="action; context: { $implicit: row }"
                          ></ng-container>
                        </ul>
                      </ng-template>
                    </div>

                    <ng-template #action let-element>
                      <ng-container *ngFor="let actionConfig of column.actions">
                        <ng-container
                          *ngTemplateOutlet="
                            actionBtn;
                            context: {
                              $implicit: {
                                case: actionConfig.case,
                                condition: isActionConditionMet(
                                  actionConfig,
                                  element
                                ),
                                name: actionConfig.name,
                                title: actionConfig.title,
                                class: actionConfig.class
                              }
                            }
                          "
                        ></ng-container>
                      </ng-container>
                    </ng-template>

                    <ng-template #actionBtn let-attr>
                      <li
                        *ngIf="attr.condition"
                        class="w-full"
                        [dxTooltip]="attr.title"
                        dxTooltipPosition="above"
                      >
                        <div
                          class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          (click)="handleAction(attr.case, row)"
                          [class]="[attr.class ?? '']"
                        >
                          <ng-icon
                            *ngIf="attr.name.includes('hero')"
                            [name]="attr.name"
                            [class]="'text-2xl ' + attr.class"
                          ></ng-icon>
                          <div
                            class="flex items-center justify-between text-[16px] font-medium"
                          >
                            {{ attr.title }}
                          </div>
                        </div>
                      </li>

                      <li *ngIf="!attr.condition" class="w-full">
                        <div
                          class="flex items-center gap-x-2 p-2.5 w-full rounded opacity-50 cursor-not-allowed"
                          [dxTooltip]="attr.title"
                          dxTooltipPosition="above"
                        >
                          <ng-icon
                            [name]="attr.name"
                            class="text-xl text-[#d1d1d1]"
                          ></ng-icon>
                          <span
                            class="text-sm text-light-text dark:text-dark-text"
                            >{{ attr.title }}</span
                          >
                        </div>
                      </li>
                    </ng-template>
                  </ng-template>
                </ng-container>

                <ng-container *ngSwitchDefault>
                  <ng-container
                    [ngTemplateOutlet]="rowTemplate || defaultRowTemplate"
                    [ngTemplateOutletContext]="{
                      row: row,
                      column: column,
                      index: getRowIndex(row)
                    }"
                  >
                  </ng-container>
                  <ng-template #defaultRowTemplate>
                    <ng-container [ngSwitch]="column.columnDef">
                      <ng-container *ngSwitchCase="'index'">
                        <div class="flex items-center justify-center">
                          <span>{{ getRowIndex(row) + 1 }}</span>
                        </div>
                      </ng-container>
                      <!-- Default rendering for columns -->
                      <ng-container
                        *ngIf="
                          column.columnDef &&
                          column.columnDef !== 'index' &&
                          column.columnDef !== 'action'
                        "
                      >
                        <div
                          class="text-light-text items-center dark:text-dark-text px-0 overflow-hidden text-ellipsis whitespace-nowrap"
                          [dxTooltip]="getOriginalText(row, column)"
                          dxTooltipPosition="below"
                          [innerHTML]="
                            column.cellRenderer
                              ? column.cellRenderer(row)
                              : row && column.columnDef
                              ? row[column.columnDef]
                              : ''
                          "
                        ></div>
                      </ng-container>
                    </ng-container>
                  </ng-template>
                </ng-container>
              </ng-container>
            </td>
          </ng-container>
        </tr>
        } @empty {
        <tr
          class="border-b border-primary-border dark:border-dark-primary-border"
        >
          <td
            [attr.colspan]="columns.length"
            class="text-light-text/60 dark:text-dark-text/60 text-center bg-transparent py-8 italic text-sm"
          >
            No data available
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>

  <!-- Custom Pagination - Always enabled -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 w-full bg-transparent text-light-text dark:text-dark-text mb-4"
  >
    <div
      class="flex items-center flex-col flex-wrap md:flex-row justify-between"
      aria-label="Table navigation"
    >
      @if (!hiddenPaginator) { @if (!uiStore.isHandset()){
      <div class="flex items-center mb-4 md:mb-0 space-x-2">
        <span
          class="text-sm font-normal text-light-text/70 dark:text-dark-text/70"
          >Rows per page:</span
        >
        <dx-form-field
          class="w-[100px]"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [ngModel]="limit"
            (ngModelChange)="onPageSizeChange($event)"
          >
            @for (size of pageSizeOptions; track $index) {
            <dx-option [value]="size" class="truncate line-clamp-1">
              {{ size }}
            </dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>

      <div
        class="text-sm font-normal text-light-text/70 dark:text-dark-text/70 mb-4 md:mb-0 block w-full md:inline md:w-auto"
      >
        Showing
        <span class="font-medium text-light-text dark:text-dark-text"
          >{{ pageIndex * limit + 1 }} -{{
            Math.min(pageIndex * limit + rows.length, count)
          }}</span
        >
        of
        <span class="font-medium text-light-text dark:text-dark-text">{{
          count
        }}</span>
      </div>

      <div class="pagination-container">
        <div class="flex items-center justify-between space-x-2">
          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="pageIndex === 0"
            [ngClass]="{ 'opacity-50 cursor-not-allowed': pageIndex === 0 }"
            (click)="onPageChange(0)"
          >
            <ng-icon
              name="heroChevronDoubleLeftMini"
              class="text-2xl"
              dxTooltip="First page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>

          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="pageIndex === 0"
            [ngClass]="{ 'opacity-50 cursor-not-allowed': pageIndex === 0 }"
            (click)="onPageChange(pageIndex - 1)"
          >
            <ng-icon
              name="heroChevronLeftMini"
              class="text-2xl"
              dxTooltip="Next page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>
          <!-- <div class="relative">
                <select
                  class="bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border rounded-xl rounded-xl py-2.5 pl-3 pr-8 text-sm appearance-none"
                  [ngModel]="pageIndex"
                  (ngModelChange)="onPageChange($event - 1)"
                >
                  <option [ngValue]="pageIndex" hidden>
                    {{ pageIndex + 1 }}/{{ Math.ceil(count / limit) }}
                  </option>
                  <option *ngFor="let page of getPageNumbers()" [value]="page">
                    {{ page }}
                  </option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-light-text/50 dark:text-dark-text/50"
                >
                  <ng-icon
                    name="heroChevronDownMini"
                    class="text-2xl"
                    [dxTooltip]="'Go to page'"
                    dxTooltipPosition="above"
                  ></ng-icon>
                </div>
              </div> -->

          <dx-form-field
            class="w-20"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <dx-select
              [ngModel]="pageIndex + 1"
              (ngModelChange)="onPageChange($event - 1)"
            >
              <!-- <dx-option [value]="pageIndex" hidden>
                    {{ pageIndex + 1 }}/{{ Math.ceil(count / limit) }}
                  </dx-option> -->
              @for (page of getPageNumbers(); track $index) {
              <dx-option [value]="page" class="truncate line-clamp-1">
                {{ page }}
              </dx-option>
              }
            </dx-select>
          </dx-form-field>

          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="(pageIndex + 1) * limit >= count"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': (pageIndex + 1) * limit >= count
            }"
            (click)="onPageChange(pageIndex + 1)"
          >
            <ng-icon
              name="heroChevronRightMini"
              class="text-2xl"
              dxTooltip="Previous page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>
          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="(pageIndex + 1) * limit >= count"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': (pageIndex + 1) * limit >= count
            }"
            (click)="onPageChange(Math.ceil(count / limit) - 1)"
          >
            <ng-icon
              name="heroChevronDoubleRightMini"
              class="text-2xl"
              dxTooltip="Last page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>
        </div>
      </div>
      } @else {
      <div
        class="w-full flex flex-col items-center justify-center space-y-2 lg:space-x-2 px-3 text-sm pt-2"
      >
        <div
          class="text-sm font-normal text-light-text/70 dark:text-dark-text/70 block md:inline md:w-auto"
        >
          Showing
          <span class="font-medium text-light-text dark:text-dark-text"
            >{{ pageIndex * limit + 1 }} -{{
              Math.min(pageIndex * limit + rows.length, count)
            }}</span
          >
          of
          <span class="font-medium text-light-text dark:text-dark-text">{{
            count
          }}</span>
        </div>
        <div class="flex w-full items-center justify-between space-x-2">
          <div class="flex items-center space-x-2">
            <span
              class="text-sm font-normal text-light-text/70 dark:text-dark-text/70"
              >Rows:</span
            >
            <dx-form-field
              class="w-20"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <dx-select
                [ngModel]="limit"
                (ngModelChange)="onPageSizeChange($event)"
              >
                @for (size of pageSizeOptions; track $index) {
                <dx-option [value]="size" class="truncate line-clamp-1">
                  {{ size }}
                </dx-option>
                }
              </dx-select>
            </dx-form-field>
          </div>
          <div class="flex items-center justify-end space-x-2">
            <span
              class="text-sm font-normal text-light-text/70 dark:text-dark-text/70"
              >Page:</span
            >
            <dx-form-field
              class="w-20"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <dx-select
                [ngModel]="pageIndex"
                (ngModelChange)="onPageChange($event - 1)"
              >
                @for (page of getPageNumbers(); track $index) {
                <dx-option [value]="page" class="truncate line-clamp-1">
                  {{ page }}
                </dx-option>
                }
              </dx-select>
            </dx-form-field>
          </div>
        </div>
      </div>
      } }
    </div>
  </div>
</div>
