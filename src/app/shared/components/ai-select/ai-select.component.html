<dx-form-field
  class="w-full mt-6"
  [style.margin-bottom]="0"
  [style.--dx-form-field-label-offset-y]="0"
  [subscriptHidden]="true"
>
  <div
    dxPrefix
    (click)="openDialogCreateAi(); $event.stopPropagation()"
    class="aspect-square flex items-center justify-center w-[38px] h-[38px] bg-primary mx-[7px] rounded-[9px] text-primary-content truncate px-0.5"
  >
    <!--    {{ userAiStore.currentAi()?.name || "Dx" | abbreviation }}-->
    <ng-icon name="heroPlusCircle" class="text-xl"></ng-icon>
  </div>
  <dx-select
    panelClass="dx-select-ai-select"
    [ngModel]="this.currentAi()"
    (ngModelChange)="selectAI($event)"
  >
    @for (ai of filteredOptions(); track $index) {
    <dx-option [value]="ai.id" class="truncate line-clamp-1">
      {{ ai.name }}
    </dx-option>
    } @empty {
    <div
      class="w-full h-full flex items-center justify-center text-gray-400 italic text-sm dark:text-white"
    >
      No AI found
    </div>
    }
  </dx-select>
</dx-form-field>
