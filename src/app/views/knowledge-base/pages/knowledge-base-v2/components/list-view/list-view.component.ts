import { SelectionModel } from '@angular/cdk/collections';
import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  PLATFORM_ID,
  Renderer2,
  signal,
  ViewChild,
} from '@angular/core';
import { DxTooltip } from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import { ClickOutsideDirective, LongPressDirective } from '@shared/directives';
import { IFile, IFolder, ISearchModel } from '@shared/models';

@Component({
  selector: 'app-list-view',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    SvgIconComponent,
    DxTooltip,
    ClickOutsideDirective,
    LongPressDirective,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
  ],
  templateUrl: './list-view.component.html',
  styleUrl: './list-view.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ListViewComponent implements OnInit, OnDestroy {
  private cdr = inject(ChangeDetectorRef);
  private platformId = inject(PLATFORM_ID);
  private renderer2 = inject(Renderer2);

  // Event listeners for cleanup
  private scrollListener?: () => void;
  private resizeListener?: () => void;
  private keydownListener?: () => void;
  private keyupListener?: () => void;

  // Signal-based inputs
  combinedList = input<(IFolder | IFile)[]>([]);
  searchModel = input.required<ISearchModel>();
  folderSelection = input.required<SelectionModel<IFolder>>();
  fileSelection = input.required<SelectionModel<IFile>>();

  // Signal-based outputs
  folderSelected = output<IFolder>();
  fileSelected = output<IFile>();
  folderDoubleClick = output<IFolder>();
  fileDoubleClick = output<IFile>();
  folderContextMenu = output<{ folder: IFolder; event: MouseEvent }>();
  fileContextMenu = output<{ file: IFile; event: MouseEvent }>();
  sortChanged = output<{ column: string; direction: 'asc' | 'desc' }>();

  // Action outputs
  folderRename = output<any>();
  folderDelete = output<any>();
  fileInfo = output<any>();
  fileRename = output<any>();
  fileMove = output<any>();
  fileDelete = output<any>();

  @ViewChild('listViewport', { static: false })
  listViewport!: CdkVirtualScrollViewport;

  // Context menu signals
  contextMenuItem = signal<
    { id: number; type: 'folder' | 'file'; item: IFolder | IFile } | undefined
  >(undefined);
  contextMenuPosition = signal<{ x: number; y: number } | undefined>(undefined);

  // Keyboard state tracking
  private isShiftPressed = signal(false);
  private isCtrlPressed = signal(false);

  // Last clicked items for range selection
  private lastClickedItem: IFolder | IFile | null = null;

  ngOnInit() {
    this.setupKeyboardListeners();
  }

  constructor() {
    // Close context menu on scroll or resize
    if (isPlatformBrowser(this.platformId)) {
      this.scrollListener = () => this.closeContextMenu();
      this.resizeListener = () => this.closeContextMenu();

      window.addEventListener('scroll', this.scrollListener, true);
      window.addEventListener('resize', this.resizeListener);
    }
  }

  ngOnDestroy() {
    // Cleanup event listeners
    if (isPlatformBrowser(this.platformId)) {
      if (this.scrollListener) {
        window.removeEventListener('scroll', this.scrollListener, true);
      }
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }
      if (this.keydownListener) {
        this.keydownListener();
      }
      if (this.keyupListener) {
        this.keyupListener();
      }
    }
  }

  /**
   * Setup keyboard event listeners for Ctrl and Shift detection
   */
  private setupKeyboardListeners() {
    if (isPlatformBrowser(this.platformId)) {
      this.keydownListener = this.renderer2.listen('document', 'keydown', (event: KeyboardEvent) => {
        if (event.key === 'Shift') {
          this.isShiftPressed.set(true);
        }
        if (event.key === 'Control' || event.key === 'Meta') { // Meta for Mac Cmd key
          this.isCtrlPressed.set(true);
        }
      });

      this.keyupListener = this.renderer2.listen('document', 'keyup', (event: KeyboardEvent) => {
        if (event.key === 'Shift') {
          this.isShiftPressed.set(false);
        }
        if (event.key === 'Control' || event.key === 'Meta') {
          this.isCtrlPressed.set(false);
        }
      });
    }
  }

  /**
   * Handle folder selection with multi-select support
   */
  onFolderClick(folder: any) {
    const selection = this.folderSelection();

    if (this.isCtrlPressed()) {
      // Ctrl+Click: Toggle selection
      if (selection.isSelected(folder)) {
        selection.deselect(folder);
      } else {
        selection.select(folder);
      }
      this.lastClickedItem = folder;
    } else if (this.isShiftPressed() && this.lastClickedItem && this.isFolder(this.lastClickedItem)) {
      // Shift+Click: Range selection (only if last clicked was also a folder)
      this.selectItemRange(this.lastClickedItem, folder);
    } else {
      // Normal click: Single selection
      selection.clear();
      this.fileSelection().clear(); // Clear file selection when selecting folders
      selection.select(folder);
      this.lastClickedItem = folder;
    }

    this.folderSelected.emit(folder);
    this.cdr.detectChanges();
  }

  /**
   * Handle file selection with multi-select support
   */
  onFileClick(file: any) {
    const selection = this.fileSelection();

    if (this.isCtrlPressed()) {
      // Ctrl+Click: Toggle selection
      if (selection.isSelected(file)) {
        selection.deselect(file);
      } else {
        selection.select(file);
      }
      this.lastClickedItem = file;
    } else if (this.isShiftPressed() && this.lastClickedItem && this.isFile(this.lastClickedItem)) {
      // Shift+Click: Range selection (only if last clicked was also a file)
      this.selectItemRange(this.lastClickedItem, file);
    } else {
      // Normal click: Single selection
      selection.clear();
      this.folderSelection().clear(); // Clear folder selection when selecting files
      selection.select(file);
      this.lastClickedItem = file;
    }

    this.fileSelected.emit(file);
    this.cdr.detectChanges();
  }

  /**
   * Select a range of items between two items of the same type
   */
  private selectItemRange(startItem: IFolder | IFile, endItem: IFolder | IFile) {
    const combinedList = this.combinedList();
    const isSelectingFolders = this.isFolder(startItem) && this.isFolder(endItem);
    const isSelectingFiles = this.isFile(startItem) && this.isFile(endItem);

    if (!isSelectingFolders && !isSelectingFiles) return;

    // Filter list to only include items of the same type
    const filteredList = combinedList.filter(item =>
      isSelectingFolders ? this.isFolder(item) : this.isFile(item)
    );

    const startIndex = filteredList.findIndex(item => item.id === startItem.id);
    const endIndex = filteredList.findIndex(item => item.id === endItem.id);

    if (startIndex === -1 || endIndex === -1) return;

    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    // Clear current selection and select range
    const selection = isSelectingFolders ? this.folderSelection() : this.fileSelection();
    selection.clear();

    for (let i = minIndex; i <= maxIndex; i++) {
      selection.select(filteredList[i] as any);
    }
  }

  onFolderDoubleClick(folder: any) {
    this.folderDoubleClick.emit(folder);
  }

  onFileDoubleClick(file: any) {
    this.fileDoubleClick.emit(file);
  }

  onRightClick(item: IFolder | IFile, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    const id = item.id || 0;
    const type = this.isFile(item) ? 'file' : 'folder';

    // Calculate menu position to avoid viewport overflow
    const menuWidth = type === 'folder' ? 245 : 166;
    const menuHeight = type === 'folder' ? 80 : 160;

    let x = event.clientX;
    let y = event.clientY;

    if (isPlatformBrowser(this.platformId)) {
      if (x + menuWidth > window.innerWidth) {
        x = window.innerWidth - menuWidth - 10;
      }

      if (y + menuHeight > window.innerHeight) {
        y = window.innerHeight - menuHeight - 10;
      }
    }

    this.contextMenuItem.set({ id, type, item });
    this.contextMenuPosition.set({ x, y });
  }

  closeContextMenu() {
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  // Utility methods
  isFolder(item: any): item is IFolder {
    return item && !('file_path' in item) && item.isFolder !== false;
  }

  isFile(item: any): item is IFile {
    return item && ('file_path' in item || item.isFolder === false);
  }

  trackByItemId(_index: number, item: IFolder | IFile): number {
    return item.id || 0;
  }

  removeSelectedItems() {
    this.folderSelection().clear();
    this.fileSelection().clear();
    this.lastClickedItem = null;
    this.cdr.detectChanges();
  }
}
