import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SelectionModel } from '@angular/cdk/collections';
import { signal } from '@angular/core';
import { GridViewComponent } from './grid-view.component';
import { IFile, IFolder, ISearchModel } from '@shared/models';

describe('GridViewComponent - Multi-Selection', () => {
  let component: GridViewComponent;
  let fixture: ComponentFixture<GridViewComponent>;
  let mockFolderSelection: SelectionModel<IFolder>;
  let mockFileSelection: SelectionModel<IFile>;

  const mockFolders: IFolder[] = [
    { id: 1, ai_id: 'ai1', parent_id: null, name: 'Folder 1', isFolder: true },
    { id: 2, ai_id: 'ai2', parent_id: null, name: 'Folder 2', isFolder: true },
    { id: 3, ai_id: 'ai3', parent_id: null, name: 'Folder 3', isFolder: true }
  ];

  const mockFiles: IFile[] = [
    { 
      id: 1, folder_id: null, name: 'File 1', ext: 'pdf', 
      metadata_columns: '', collection_id: 1, size: 1024, 
      text_content: '', url: '', file_path: '', status: 'COMPLETED',
      created_at: '2024-01-01', updated_at: '2024-01-01', isFolder: false 
    },
    { 
      id: 2, folder_id: null, name: 'File 2', ext: 'txt', 
      metadata_columns: '', collection_id: 1, size: 2048, 
      text_content: '', url: '', file_path: '', status: 'COMPLETED',
      created_at: '2024-01-01', updated_at: '2024-01-01', isFolder: false 
    }
  ];

  const mockSearchModel: ISearchModel = {
    name: '', collection_id: 1, folder_id: 0, file_type: '', file_status: '',
    order: 'DESC', sort_by: 'updated_at', page: 1, per_page: 50, id: 0
  };

  beforeEach(async () => {
    mockFolderSelection = new SelectionModel<IFolder>(true, []);
    mockFileSelection = new SelectionModel<IFile>(true, []);

    await TestBed.configureTestingModule({
      imports: [GridViewComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(GridViewComponent);
    component = fixture.componentInstance;

    // Set up component inputs using signals
    fixture.componentRef.setInput('folderList', mockFolders);
    fixture.componentRef.setInput('fileList', mockFiles);
    fixture.componentRef.setInput('searchModel', mockSearchModel);
    fixture.componentRef.setInput('folderSelection', mockFolderSelection);
    fixture.componentRef.setInput('fileSelection', mockFileSelection);

    fixture.detectChanges();
  });

  describe('Single Click Selection', () => {
    it('should select single folder and clear previous selection', () => {
      // Pre-select a folder
      mockFolderSelection.select(mockFolders[0]);
      expect(mockFolderSelection.selected.length).toBe(1);

      // Click another folder
      component.onFolderClick(mockFolders[1]);

      // Should clear previous and select new
      expect(mockFolderSelection.selected.length).toBe(1);
      expect(mockFolderSelection.isSelected(mockFolders[1])).toBe(true);
      expect(mockFolderSelection.isSelected(mockFolders[0])).toBe(false);
    });

    it('should select single file and clear previous selection', () => {
      // Pre-select a file
      mockFileSelection.select(mockFiles[0]);
      expect(mockFileSelection.selected.length).toBe(1);

      // Click another file
      component.onFileClick(mockFiles[1]);

      // Should clear previous and select new
      expect(mockFileSelection.selected.length).toBe(1);
      expect(mockFileSelection.isSelected(mockFiles[1])).toBe(true);
      expect(mockFileSelection.isSelected(mockFiles[0])).toBe(false);
    });
  });

  describe('Ctrl+Click Multi-Selection', () => {
    it('should toggle folder selection with Ctrl+Click', () => {
      // Simulate Ctrl key pressed
      (component as any).isCtrlPressed.set(true);

      // First click - should select
      component.onFolderClick(mockFolders[0]);
      expect(mockFolderSelection.isSelected(mockFolders[0])).toBe(true);

      // Second click on different folder - should add to selection
      component.onFolderClick(mockFolders[1]);
      expect(mockFolderSelection.selected.length).toBe(2);
      expect(mockFolderSelection.isSelected(mockFolders[0])).toBe(true);
      expect(mockFolderSelection.isSelected(mockFolders[1])).toBe(true);

      // Third click on already selected folder - should deselect
      component.onFolderClick(mockFolders[0]);
      expect(mockFolderSelection.selected.length).toBe(1);
      expect(mockFolderSelection.isSelected(mockFolders[0])).toBe(false);
      expect(mockFolderSelection.isSelected(mockFolders[1])).toBe(true);
    });

    it('should toggle file selection with Ctrl+Click', () => {
      // Simulate Ctrl key pressed
      (component as any).isCtrlPressed.set(true);

      // First click - should select
      component.onFileClick(mockFiles[0]);
      expect(mockFileSelection.isSelected(mockFiles[0])).toBe(true);

      // Second click on different file - should add to selection
      component.onFileClick(mockFiles[1]);
      expect(mockFileSelection.selected.length).toBe(2);
      expect(mockFileSelection.isSelected(mockFiles[0])).toBe(true);
      expect(mockFileSelection.isSelected(mockFiles[1])).toBe(true);

      // Third click on already selected file - should deselect
      component.onFileClick(mockFiles[0]);
      expect(mockFileSelection.selected.length).toBe(1);
      expect(mockFileSelection.isSelected(mockFiles[0])).toBe(false);
      expect(mockFileSelection.isSelected(mockFiles[1])).toBe(true);
    });
  });

  describe('Shift+Click Range Selection', () => {
    it('should select range of folders with Shift+Click', () => {
      // First click to set starting point
      component.onFolderClick(mockFolders[0]);
      expect(mockFolderSelection.selected.length).toBe(1);

      // Simulate Shift key pressed
      (component as any).isShiftPressed.set(true);

      // Shift+Click on third folder should select range
      component.onFolderClick(mockFolders[2]);

      // Should select all folders in range (0, 1, 2)
      expect(mockFolderSelection.selected.length).toBe(3);
      expect(mockFolderSelection.isSelected(mockFolders[0])).toBe(true);
      expect(mockFolderSelection.isSelected(mockFolders[1])).toBe(true);
      expect(mockFolderSelection.isSelected(mockFolders[2])).toBe(true);
    });

    it('should select range of files with Shift+Click', () => {
      // First click to set starting point
      component.onFileClick(mockFiles[0]);
      expect(mockFileSelection.selected.length).toBe(1);

      // Simulate Shift key pressed
      (component as any).isShiftPressed.set(true);

      // Shift+Click on second file should select range
      component.onFileClick(mockFiles[1]);

      // Should select both files
      expect(mockFileSelection.selected.length).toBe(2);
      expect(mockFileSelection.isSelected(mockFiles[0])).toBe(true);
      expect(mockFileSelection.isSelected(mockFiles[1])).toBe(true);
    });
  });

  describe('Event Emission', () => {
    it('should emit folderSelected event on folder click', () => {
      spyOn(component.folderSelected, 'emit');
      
      component.onFolderClick(mockFolders[0]);
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith(mockFolders[0]);
    });

    it('should emit fileSelected event on file click', () => {
      spyOn(component.fileSelected, 'emit');
      
      component.onFileClick(mockFiles[0]);
      
      expect(component.fileSelected.emit).toHaveBeenCalledWith(mockFiles[0]);
    });
  });
});
