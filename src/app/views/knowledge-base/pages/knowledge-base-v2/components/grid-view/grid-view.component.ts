import { SelectionModel } from '@angular/cdk/collections';
import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  PLATFORM_ID,
  Renderer2,
  signal,
  ViewChild,
} from '@angular/core';
import { DxTooltip } from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import { ClickOutsideDirective, LongPressDirective } from '@shared/directives';
import { IFile, IFolder, ISearchModel } from '@shared/models';

@Component({
  selector: 'app-grid-view',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    SvgIconComponent,
    DxTooltip,
    ClickOutsideDirective,
    LongPressDirective,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
  ],
  templateUrl: './grid-view.component.html',
  styleUrl: './grid-view.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GridViewComponent implements OnInit, OnDestroy {
  private cdr = inject(ChangeDetectorRef);
  private platformId = inject(PLATFORM_ID);
  private renderer2 = inject(Renderer2);

  // Event listeners for cleanup
  private scrollListener?: () => void;
  private resizeListener?: () => void;
  private keydownListener?: () => void;
  private keyupListener?: () => void;

  // Signal-based inputs
  folderList = input<IFolder[]>([]);
  fileList = input<IFile[]>([]);
  searchModel = input.required<ISearchModel>();
  folderSelection = input.required<SelectionModel<IFolder>>();
  fileSelection = input.required<SelectionModel<IFile>>();

  // Signal-based outputs
  folderSelected = output<IFolder>();
  fileSelected = output<IFile>();
  folderDoubleClick = output<IFolder>();
  fileDoubleClick = output<IFile>();
  folderContextMenu = output<{ folder: IFolder; event: MouseEvent }>();
  fileContextMenu = output<{ file: IFile; event: MouseEvent }>();

  // New specific action outputs
  folderRename = output<any>();
  folderDelete = output<any>();
  fileInfo = output<any>();
  fileRename = output<any>();
  fileMove = output<any>();
  fileDelete = output<any>();
  refreshRequested = output<void>();

  @ViewChild('folderViewport', { static: false })
  folderViewport!: CdkVirtualScrollViewport;
  @ViewChild('fileViewport', { static: false })
  fileViewport!: CdkVirtualScrollViewport;

  // Context menu signals
  contextMenuItem = signal<
    { id: number; type: 'folder' | 'file'; item: IFolder | IFile } | undefined
  >(undefined);
  contextMenuPosition = signal<{ x: number; y: number } | undefined>(undefined);
  itemSelected = signal<{ id: number; type: 'folder' | 'file' } | undefined>(
    undefined
  );

  // Keyboard state tracking
  private isShiftPressed = signal(false);
  private isCtrlPressed = signal(false);

  // Last clicked items for range selection
  private lastClickedFolder: IFolder | null = null;
  private lastClickedFile: IFile | null = null;

  ngOnInit() {
    this.setupKeyboardListeners();
  }

  constructor() {
    // Close context menu on scroll or resize
    if (isPlatformBrowser(this.platformId)) {
      this.scrollListener = () => this.closeContextMenu();
      this.resizeListener = () => this.closeContextMenu();

      window.addEventListener('scroll', this.scrollListener, true);
      window.addEventListener('resize', this.resizeListener);
    }
  }

  ngOnDestroy() {
    // Cleanup event listeners
    if (isPlatformBrowser(this.platformId)) {
      if (this.scrollListener) {
        window.removeEventListener('scroll', this.scrollListener, true);
      }
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }
      if (this.keydownListener) {
        this.keydownListener();
      }
      if (this.keyupListener) {
        this.keyupListener();
      }
    }
  }

  /**
   * Setup keyboard event listeners for Ctrl and Shift detection
   */
  private setupKeyboardListeners() {
    if (isPlatformBrowser(this.platformId)) {
      this.keydownListener = this.renderer2.listen('document', 'keydown', (event: KeyboardEvent) => {
        if (event.key === 'Shift') {
          this.isShiftPressed.set(true);
        }
        if (event.key === 'Control' || event.key === 'Meta') { // Meta for Mac Cmd key
          this.isCtrlPressed.set(true);
        }
      });

      this.keyupListener = this.renderer2.listen('document', 'keyup', (event: KeyboardEvent) => {
        if (event.key === 'Shift') {
          this.isShiftPressed.set(false);
        }
        if (event.key === 'Control' || event.key === 'Meta') {
          this.isCtrlPressed.set(false);
        }
      });
    }
  }

  /**
   * Handle folder selection with multi-select support
   */
  onFolderClick(folder: IFolder) {
    const selection = this.folderSelection();

    if (this.isCtrlPressed()) {
      // Ctrl+Click: Toggle selection
      if (selection.isSelected(folder)) {
        selection.deselect(folder);
      } else {
        selection.select(folder);
      }
      this.lastClickedFolder = folder;
    } else if (this.isShiftPressed() && this.lastClickedFolder) {
      // Shift+Click: Range selection
      this.selectFolderRange(this.lastClickedFolder, folder);
    } else {
      // Normal click: Single selection
      selection.clear();
      selection.select(folder);
      this.lastClickedFolder = folder;
    }

    this.folderSelected.emit(folder);
    this.cdr.detectChanges();
  }

  /**
   * Handle file selection with multi-select support
   */
  onFileClick(file: IFile) {
    const selection = this.fileSelection();

    if (this.isCtrlPressed()) {
      // Ctrl+Click: Toggle selection
      if (selection.isSelected(file)) {
        selection.deselect(file);
      } else {
        selection.select(file);
      }
      this.lastClickedFile = file;
    } else if (this.isShiftPressed() && this.lastClickedFile) {
      // Shift+Click: Range selection
      this.selectFileRange(this.lastClickedFile, file);
    } else {
      // Normal click: Single selection
      selection.clear();
      selection.select(file);
      this.lastClickedFile = file;
    }

    this.fileSelected.emit(file);
    this.cdr.detectChanges();
  }

  /**
   * Select a range of folders between two folders
   */
  private selectFolderRange(startFolder: IFolder, endFolder: IFolder) {
    const folderList = this.folderList();
    const selection = this.folderSelection();

    const startIndex = folderList.findIndex(f => f.id === startFolder.id);
    const endIndex = folderList.findIndex(f => f.id === endFolder.id);

    if (startIndex === -1 || endIndex === -1) return;

    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    // Clear current selection and select range
    selection.clear();
    for (let i = minIndex; i <= maxIndex; i++) {
      selection.select(folderList[i]);
    }
  }

  /**
   * Select a range of files between two files
   */
  private selectFileRange(startFile: IFile, endFile: IFile) {
    const fileList = this.fileList();
    const selection = this.fileSelection();

    const startIndex = fileList.findIndex(f => f.id === startFile.id);
    const endIndex = fileList.findIndex(f => f.id === endFile.id);

    if (startIndex === -1 || endIndex === -1) return;

    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    // Clear current selection and select range
    selection.clear();
    for (let i = minIndex; i <= maxIndex; i++) {
      selection.select(fileList[i]);
    }
  }

  onFolderDoubleClick(folder: IFolder) {
    this.folderDoubleClick.emit(folder);
  }

  onFileDoubleClick(file: IFile) {
    this.fileDoubleClick.emit(file);
  }

  onFolderContextMenu(folder: IFolder, event: MouseEvent) {
    this.folderContextMenu.emit({ folder, event });
  }

  onFileContextMenu(file: IFile, event: MouseEvent) {
    this.fileContextMenu.emit({ file, event });
  }

  // Context menu action methods
  onFolderRename(folder: IFolder) {
    this.folderRename.emit(folder);
  }

  onFolderDelete(folder: IFolder) {
    this.folderDelete.emit(folder);
  }

  onFileInfo(file: IFile) {
    this.fileInfo.emit(file);
  }

  onFileRename(file: IFile) {
    this.fileRename.emit(file);
  }

  onFileMove(file: IFile) {
    this.fileMove.emit(file);
  }

  onFileDelete(file: IFile) {
    this.fileDelete.emit(file);
  }

  onRefreshClick() {
    this.refreshRequested.emit();
  }

  onRightClick(item: IFolder | IFile, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    const id = item.id || 0;
    const type = 'file_path' in item ? 'file' : 'folder';

    // Calculate menu position to avoid viewport overflow
    const menuWidth = type === 'folder' ? 245 : 166;
    const menuHeight = type === 'folder' ? 80 : 160;

    let x = event.clientX;
    let y = event.clientY;

    if (isPlatformBrowser(this.platformId)) {
      if (x + menuWidth > window.innerWidth) {
        x = window.innerWidth - menuWidth - 10;
      }

      if (y + menuHeight > window.innerHeight) {
        y = window.innerHeight - menuHeight - 10;
      }
    }

    this.contextMenuItem.set({ id, type, item });
    this.contextMenuPosition.set({ x, y });
  }

  closeContextMenu() {
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  // Utility methods
  trackByFolderId(_index: number, folder: IFolder): number {
    return folder.id || 0;
  }

  trackByFileId(_index: number, file: IFile): number {
    return file.id || 0;
  }

  removeSelectedFolderOrFile() {
    this.folderSelection().clear();
    this.fileSelection().clear();
    this.lastClickedFolder = null;
    this.lastClickedFile = null;
    this.cdr.detectChanges();
  }

  openMenuActions(item: IFolder | IFile) {
    const id = item.id || 0;
    const type = item.isFolder === false ? 'file' : 'folder';
    this.itemSelected.set({ id, type });
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  getType(item: IFolder | IFile): 'file' | 'folder' {
    return item.isFolder === false ? 'file' : 'folder';
  }

  closeAllMenus() {
    this.itemSelected.set(undefined);
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }
}
