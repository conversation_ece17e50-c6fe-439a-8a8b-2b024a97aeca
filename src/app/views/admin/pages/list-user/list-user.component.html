@if (!uiStore.isHandset()) {
<div class="h-full flex flex-col overflow-hidden">
  <div
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    User Management
  </div>
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-4 flex-wrap">
        <dx-form-field
          class="w-full md:w-72"
          id="search"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="changeFilter()"
            [type]="'text'"
            placeholder="Search by Name/Email"
          />
        </dx-form-field>
        <dx-form-field
          class="w-full md:w-48"
          id="role-filter"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.role"
            (selectionChange)="changeFilter()"
          >
            @for (role of roleOptions; track $index) {
            <dx-option [value]="role.value">{{ role.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
        <dx-form-field
          class="w-full md:w-54"
          id="plan-filter"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.plan_id"
            (selectionChange)="changeFilter()"
          >
            @for (plan of planOptions; track $index) {
            <dx-option [value]="plan.value">{{ plan.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
        <dx-form-field
          class="w-full md:w-64"
          id="referral-filter"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.referral_email"
            (selectionChange)="changeFilter()"
          >
            <dx-option>
              <dx-form-field
                [subscriptHidden]="true"
                (click)="$event.stopPropagation()"
              >
                <input
                  dxInput
                  [(ngModel)]="searchReferralEmail"
                  (ngModelChange)="onSearchReferralEmail()"
                />
              </dx-form-field>
            </dx-option>
            @for (referral of referralOptionsFilted; track $index) {
            <dx-option [value]="referral.value">{{ referral.label }}</dx-option>
            } @empty {
            <dx-option>No Referral email.</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>
      <div class="flex items-center justify-end">
        <button dx-button="filled" (click)="openCreateUserDialog()">
          <div class="flex items-center justify-between space-x-1">
            <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
            <span class="text-sm font-medium">Add User</span>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        class="w-full"
        [rows]="listUser"
        [columns]="columns"
        [pageIndex]="paramModel.pageIndex"
        [limit]="paramModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        [loading]="isLoading()"
      >
      </app-data-table>
    </div>
  </div>
</div>
} @else {
<app-mobile-header [title]="'Users'" [backFn]="backFn"></app-mobile-header>
<div
  class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div class="flex items-center justify-between space-x-4">
    <dx-form-field
      class="flex-1 w-full"
      id="search"
      [style.margin-bottom]="0"
      [style.--dx-form-field-label-offset-y]="0"
      [subscriptHidden]="true"
    >
      <app-svg-icon
        dxPrefix
        type="icSearch"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        [(ngModel)]="searchModel.key_word"
        (ngModelChange)="changeFilter()"
        [type]="'text'"
        placeholder="Search by Name/Email"
      />
    </dx-form-field>

    <div class="flex-shrink-0 flex items-center space-x-1">
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        (click)="openCreateUserDialog()"
      >
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        ></app-svg-icon>
      </div>
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
      >
        <app-svg-icon
          type="icFilter"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="viewFilter.set(true)"
        ></app-svg-icon>
      </div>
    </div>
  </div>
  <div
    class="bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border rounded-[12px]"
  >
    <div class="m-h-table">
      <app-data-table
        class="h-full"
        [rows]="listUser"
        [columns]="columns"
        [pageIndex]="paramModel.pageIndex"
        [limit]="paramModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        [loading]="isLoading()"
      >
      </app-data-table>
    </div>
  </div>
</div>

<app-mobile-drawer [visible]="viewFilter()">
  <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
    <app-mobile-header
      [title]="'Filter'"
      [backFn]="closeFn"
      [hideMenu]="true"
    ></app-mobile-header>
    <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
      <dx-form-field
        class="w-full"
        id="role-filter"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Role</dx-label>
        <dx-select
          [(ngModel)]="searchModel.role"
          (selectionChange)="changeFilter()"
        >
          @for (role of roleOptions; track $index) {
          <dx-option [value]="role.value">{{ role.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>
      <dx-form-field
        class="w-full"
        id="plan-filter"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Plan</dx-label>
        <dx-select
          [(ngModel)]="searchModel.plan_id"
          (selectionChange)="changeFilter()"
        >
          @for (plan of planOptions; track $index) {
          <dx-option [value]="plan.value">{{ plan.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>
      <dx-form-field
        class="w-full"
        id="referral-filter"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Referral</dx-label>
        <dx-select
          [(ngModel)]="searchModel.referral_email"
          (selectionChange)="changeFilter()"
        >
          <dx-option>
            <dx-form-field
              [subscriptHidden]="true"
              (click)="$event.stopPropagation()"
            >
              <input
                dxInput
                [(ngModel)]="searchReferralEmail"
                (ngModelChange)="onSearchReferralEmail()"
              />
            </dx-form-field>
          </dx-option>
          @for (referral of referralOptionsFilted; track $index) {
          <dx-option [value]="referral.value">{{ referral.label }}</dx-option>
          } @empty {
          <dx-option>No Referral email.</dx-option>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>
</app-mobile-drawer>
}

<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
    @case ('email_verified') {
      <div
        class="flex items-center justify-center"
        [ngStyle]="{ 'justify-content': column.align }"
      >
        <app-svg-icon
          [type]="row[column.columnDef] ? 'icCircleCheck' : 'icCircleClose'"
          class="w-6 h-6"
          [ngClass]="{
            '!text-success dark:!text-dark-success': row[column.columnDef],
            '!text-error dark:!text-dark-error': !row[column.columnDef]
          }"
          [dxTooltip]="
            roleUser === ROLE_ACCOUNT.SUPER_ADMIN
              ? 'Resend email to activate account'
              : ''
          "
          (click)="!row[column.columnDef] && resendEmail(row)"
        >
        </app-svg-icon>
      </div>
    }

    @case ('last_login') {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        {{ row[column.columnDef] | date : 'dd/MM/yyyy HH:mm:ss' }}
      </div>
    }

    @case ('plan') {
      <div
        class="flex"
        [ngClass]="{ 'cursor-pointer': acceptUpdatePlan(row) }"
        [ngStyle]="{ 'justify-content': column.align }"
        (click)="acceptUpdatePlan(row) ? changePlanBySuperAdmin(row) : null"
      >
        @if (row[column.columnDef]) {
          <div
            class="px-4 border rounded-full text-center truncate"
            [ngClass]="handleClassesPlan(row[column.columnDef])"
            [dxTooltip]="
              handleTimePlanTooltip(row, 'start_date') +
              '&#13;&#10;' +
              handleTimePlanTooltip(row, 'end_date')
            "
            dxTooltipPosition="right"
          >
            {{ handleNamePlan(row[column.columnDef]) }}
          </div>
        } @else {
          <div
            class="px-2 border rounded text-center border-light-red text-light-red hover:bg-light-red hover:text-light-text dark:hover:text-dark-text"
          >
            None
          </div>
        }
      </div>
    }

    @case ('role') {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        <div
          class="px-4 rounded-full text-center"
          [ngClass]="{
            'bg-primary text-primary-content':
              row[column.columnDef] === ROLE_ACCOUNT.USER,
            'bg-success text-success-content':
              row[column.columnDef] === ROLE_ACCOUNT.PARTNER,
            'bg-warning text-warning-content':
              row[column.columnDef] === ROLE_ACCOUNT.ADMIN
          }"
        >
          {{ row[column.columnDef] | titlecase }}
        </div>
      </div>
    }

    @case ('number_of_ai') {
      <div
        class="flex cursor-pointer"
        [ngStyle]="{ 'justify-content': column.align }"
        (click)="showAIOwner(row)"
      >
        <div class="text-primary dark:text-dark-primary">
          {{ row[column.columnDef] }}
        </div>
      </div>
    }

    @default {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        {{ row[column.columnDef] }}
      </div>
    }
  }
</ng-template>

<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <app-svg-icon
      type="icMoreHorizontal"
      class="w-6 h-6 flex items-center justify-center cursor-pointer hover:opacity-80"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    ></app-svg-icon>
    @if (row.isActions && !row.isContextMenu) {
    <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="trigger"
    [cdkConnectedOverlayOpen]="true"
    [cdkConnectedOverlayPush]="true"
    [cdkConnectedOverlayPositions]="[
      { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'top', offsetY: 10 },
      { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'bottom', offsetY: 10 }
    ]"
  >
    <ul
      class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
      (clickOutside)="row.isActions = false; row.isContextMenu = false;"
    >
      @let userMenuTemplate = userMenu;
      <ng-container [ngTemplateOutlet]="userMenuTemplate" [ngTemplateOutletContext]="{ row: row }"></ng-container>
    </ul>
  </ng-template>
  }

  <!-- Fixed menu khi click phải -->
  @if (row.isActions && row.isContextMenu) {
  <ul
    class="fixed z-50 w-[245px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    [style.left.px]="row.contextMenuX"
    [style.top.px]="row.contextMenuY"
    (clickOutside)="row.isActions = false; row.isContextMenu = false;"
  >
    @let userMenuTemplate2 = userMenu;
    <ng-container [ngTemplateOutlet]="userMenuTemplate2" [ngTemplateOutletContext]="{ row: row }"></ng-container>
  </ul>
  }
  </div>
</ng-template>

<ng-template #userMenu let-row="row">
  <!-- Activate user -->
  @if (!row.email_verified && currentUser.role === ROLE_ACCOUNT.ADMIN) {
    <li>
      <button
        [disabled]="row.role === ROLE_ACCOUNT.ADMIN"
        (click)="
          handleAction('activate', row, null);
          row.isActions = false; row.isContextMenu = false;
          $event.stopPropagation()
        "
        class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
        title="Activate user"
      >
        <app-custom-icon
          iconName="faUserCheck"
          [size]="24"
          class="flex items-center justify-center dark:text-dark-green"
        ></app-custom-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Activate user
        </div>
      </button>
    </li>
  }

  <!-- Renew plan -->
  <li>
    <button
      (click)="
        currentUser.email === EMAIL_SUPER_ADMIN &&
        row.email !== EMAIL_SUPER_ADMIN &&
        row.plan_id !== 1 &&
        row.email_verified
          ? handleAction('renew', row, null)
          : null;
          row.isActions = false; row.isContextMenu = false;
        $event.stopPropagation()
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      title="Renew plan usage period"
      [disabled]="
        !(
          currentUser.email === EMAIL_SUPER_ADMIN &&
          row.email !== EMAIL_SUPER_ADMIN &&
          row.plan_id !== 1 &&
          row.email_verified
        )
      "
      [ngClass]="{
        'opacity-50 cursor-not-allowed text-neutral-content dark:text-dark-neutral-content':
          !(
            currentUser.email === EMAIL_SUPER_ADMIN &&
            row.email !== EMAIL_SUPER_ADMIN &&
            row.plan_id !== 1 &&
            row.email_verified
          )
      }"
    >
      <app-svg-icon
        type="icSync"
        class="w-6 h-6 flex items-center justify-center"
      ></app-svg-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        Renew plan
      </div>
    </button>
  </li>

  <!-- Change password -->
  @if (row.email !== EMAIL_SUPER_ADMIN || currentUser.email === EMAIL_SUPER_ADMIN) {
    <li>
      <button
        (click)="
          handleAction('password', row, null);
          row.isActions = false; row.isContextMenu = false;
          $event.stopPropagation()
        "
        class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
        title="Change password user"
        [disabled]="
          !(
            row.email !== EMAIL_SUPER_ADMIN ||
            currentUser.email === EMAIL_SUPER_ADMIN
          )
        "
        [ngClass]="{
          'opacity-50 cursor-not-allowed text-neutral-content dark:text-dark-neutral-content':
            !(
              row.email !== EMAIL_SUPER_ADMIN ||
              currentUser.email === EMAIL_SUPER_ADMIN
            )
        }"
      >
        <app-svg-icon
          type="icDialpad"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Change password
        </div>
      </button>
    </li>
  }

  <!-- Change role -->
  @if (currentUser.email === EMAIL_SUPER_ADMIN || (currentUser.role === ROLE_ACCOUNT.ADMIN && row.role !== ROLE_ACCOUNT.ADMIN)) {
    <li>
      <button
        (click)="
          handleAction('role', row, null);
          row.isActions = false; row.isContextMenu = false;
          $event.stopPropagation()
        "
        class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
        title="Change role user"
        [disabled]="
          !(
            currentUser.email === EMAIL_SUPER_ADMIN ||
            (currentUser.role === ROLE_ACCOUNT.ADMIN &&
              row.role !== ROLE_ACCOUNT.ADMIN)
          )
        "
        [ngClass]="{
          'opacity-50 cursor-not-allowed text-neutral-content dark:text-dark-neutral-content':
            !(
              currentUser.email === EMAIL_SUPER_ADMIN ||
              (currentUser.role === ROLE_ACCOUNT.ADMIN &&
                row.role !== ROLE_ACCOUNT.ADMIN)
            )
        }"
      >
        <app-svg-icon
          type="icUserShield"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Change role
        </div>
      </button>
    </li>
  }

  <!-- Delete user -->
  <li>
    <button
      (click)="
        !row.isDeleting &&
        row.email !== EMAIL_SUPER_ADMIN &&
        row.email !== currentUser.email
          ? handleAction('delete', row, null)
          : null;
          row.isActions = false; row.isContextMenu = false;
        $event.stopPropagation()
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      title="Delete user"
      [disabled]="
        !(
          !row.isDeleting &&
          row.email !== EMAIL_SUPER_ADMIN &&
          row.email !== currentUser.email
        )
      "
      [ngClass]="{
        'opacity-50 cursor-not-allowed text-neutral-content dark:text-dark-neutral-content':
          !(
            !row.isDeleting &&
            row.email !== EMAIL_SUPER_ADMIN &&
            row.email !== currentUser.email
          )
      }"
    >
      <app-svg-icon
        type="icTrash"
        class="w-6 h-6 flex items-center justify-center"
      ></app-svg-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        Delete
      </div>
    </button>
  </li>
  <!-- Spinner khi đang xóa -->
  @if (row.isDeleting) {
    <li>
      <dx-spinner [diameter]="24"></dx-spinner>
    </li>
  }
</ng-template>
