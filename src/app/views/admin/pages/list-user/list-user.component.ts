import { OverlayModule, RepositionScrollStrategy } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  ConfirmDialogComponent,
  CustomIconComponent,
  DataTableComponent,
  IColumn,
  MobileDrawerComponent,
  MobileHeaderComponent,
  SvgIconComponent,
} from '@shared/components';
import { ClickOutsideDirective } from '@shared/directives';
import {
  PlanService,
  SettingsService,
  UserManagementService,
} from '@shared/services';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { Observable, Subject } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';

import { Router } from '@angular/router';
import { APP_ROUTES } from '@core/constants';
import { UIStore, UserAiStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxPrefix,
  DxProgressSpinner,
  DxSelect,
  DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import {
  heroArrowPath,
  heroCheckCircle,
  heroEllipsisHorizontal,
  heroEye,
  heroEyeSlash,
  heroPencilSquare,
  heroTrash,
  heroXCircle,
} from '@ng-icons/heroicons/outline';
import {
  EMAIL_SUPER_ADMIN,
  ROLE_ACCOUNT,
  TYPE_PLAN,
} from '@shared/app.constant';
import { IUserInfo } from '@shared/models';
import { AddUserComponent } from '@views/admin/pages/list-user/add-user/add-user.component';
import { ChangePassDialogComponent } from '@views/admin/pages/list-user/change-pass-dialog/change-pass-dialog.component';
import { ChangeSubComponent } from '@views/admin/pages/list-user/change-sub/change-sub.component';
import { ConfirmRoleComponent } from '@views/admin/pages/list-user/confirm-role/confirm-role.component';
import { ListUserAisComponent } from '@views/admin/pages/list-user/list-user-ais/list-user-ais.component';
import { ActivatedRoute, Params } from '@angular/router';

@Component({
  selector: 'app-list-user',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    OverlayModule,
    NgIconsModule,
    DataTableComponent,
    ClickOutsideDirective,
    CustomIconComponent,
    SvgIconComponent,
    DxButton,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    DxTooltip,
    DxPrefix,
    DxProgressSpinner,
    MobileHeaderComponent,
    MobileDrawerComponent,
    DxLabel,
  ],
  templateUrl: './list-user.component.html',
  styleUrls: ['./list-user.component.css'],
  providers: [
    provideIcons({
      heroCheckCircle,
      heroXCircle,
      heroEllipsisHorizontal,
      heroArrowPath,
      heroTrash,
      heroEye,
      heroEyeSlash,
      heroPencilSquare,
    }),
  ],
})
export class ListUserComponent implements OnInit {
  // dashboardComponent = DashboardComponent;
  viewFilter = signal(false);
  isLoading = signal(false);
  formGroup: FormGroup = new FormGroup({});
  // dropdownOpen: boolean = false;
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  count: number = 0;
  columns: IColumn[] = [
    {
      columnDef: 'index',
      headerName: 'No.',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'fullName',
      headerName: 'Full name',
      flex: 0.4,
      minWidth: '180px',
    },
    {
      columnDef: 'email',
      headerName: 'Email',
      flex: 0.4,
      minWidth: '180px',
    },
    {
      columnDef: 'plan',
      headerName: 'Plan',
      flex: 0.2,
      minWidth: '80px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'role',
      headerName: 'Role',
      flex: 0.2,
      minWidth: '80px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'email_verified',
      headerName: 'Email verified',
      flex: 0.2,
      minWidth: '80px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'last_login',
      headerName: 'Last login',
      flex: 0.3,
      minWidth: '120px',
    },
    {
      columnDef: 'referral_agency_email',
      headerName: 'Referrer email',
      flex: 0.4,
      minWidth: '180px',
    },
    {
      columnDef: 'number_of_ai',
      headerName: 'Number of AI',
      flex: 0.2,
      minWidth: '80px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'action',
      headerName: 'Action',
      flex: 0.1,
      minWidth: '120px',
      alignHeader: 'center',
      align: 'center',
    },
  ];
  listUser: IUserInfo[] = [];

  searchModel: any = {
    key_word: null,
    role: '',
    plan_id: '0',
    referral_email: '',
  };
  paramModel: any = {
    pageIndex: 0,
    pageSize: 10,
  };
  listRole = [
    {
      label: 'All',
      code: '',
    },
    {
      label: 'User',
      code: ROLE_ACCOUNT.USER,
    },
  ];
  roleOptions: any[] = [];
  planOptions: any[] = [];
  referralOptions: any[] = [];
  referralOptionsFilted: any[] = [];
  LLMList = [{ name: 'Chatgpt' }];
  listPlan = [];
  listPlanSearch: any[] = [];
  ROLE_ACCOUNT = ROLE_ACCOUNT;

  EMAIL_SUPER_ADMIN = EMAIL_SUPER_ADMIN;
  newSubscription: any;
  listUserSearch: any[] = [];
  scrollStrategy: RepositionScrollStrategy | null = null;
  searchReferralEmail: string = '';
  roleUser: ROLE_ACCOUNT = ROLE_ACCOUNT.USER;
  currentUser = { email: '', role: ROLE_ACCOUNT.USER };
  selectedLLM: any;
  apiKeyInput: any;
  listAI: any;
  aiChangeLLMId: any;
  aiNameFilter: string = '';
  filterAI: { user_id: number; name: string } = { user_id: 0, name: '' };
  closeFn!: () => void;

  searchSubject: Subject<string> = new Subject<string>();

  uiStore = inject(UIStore);
  private userManagementService = inject(UserManagementService);
  private userStore = inject(UserAiStore);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);
  private planService = inject(PlanService);
  private settingsService = inject(SettingsService);
  backFn = () => {
    this.router.navigate([APP_ROUTES.MENU]);
  };
  constructor() {
    this.closeFn = this.closeFilter.bind(this);

    effect(() => {
      if (this.userStore.currentUser()) {
        const roleValue = this.userStore.currentUser()?.role;
        const roleKey = Object.keys(ROLE_ACCOUNT).find(
          (key) => ROLE_ACCOUNT[key as keyof typeof ROLE_ACCOUNT] === roleValue
        ) as keyof typeof ROLE_ACCOUNT;
        this.currentUser = {
          email: this.userStore.currentUser()?.email || '',
          role: ROLE_ACCOUNT[roleKey] || ROLE_ACCOUNT.USER,
        };
      }
    });
  }

  ngOnInit() {
    this.paramModel = {
      pageIndex: 0,
      pageSize: 10,
    };
    this.searchModel.pageSize = this.paramModel.pageSize;
    this.getAllListPlan();
    // Đọc params từ URL
    this.route.queryParams.subscribe((params: Params) => {
      // Đọc filter
      this.searchModel.key_word = params['key_word'] ?? null;
      this.searchModel.role = params['role'] ?? '';
      this.searchModel.plan_id = params['plan_id'] ?? '0';
      this.searchModel.referral_email = params['referral_email'] ?? '';
      // Đọc phân trang
      this.paramModel.pageIndex = params['pageIndex'] ? +params['pageIndex'] : 0;
      this.paramModel.pageSize = params['pageSize'] ? +params['pageSize'] : 10;
      this.searchModel.pageSize = this.paramModel.pageSize;
      // Gọi search
      this.doSearch();
    });
    this.initReferralOptions();

    this.userManagementService
      .getListUserHaveReferral()
      .subscribe((res: IUserInfo[]) => {
        const allUser = {
          email: '',
          fullName: 'All',
        };
        if (res && res.length !== 0) {
          this.listUserSearch = res.map((item) => ({
            ...item,
            fullName: item.first_name + ' ' + item.last_name,
            isDeleting: false,
          }));
        } else {
          this.listUserSearch = [];
        }
        this.listUserSearch.unshift(allUser);
      });

    this.searchSubject.pipe(debounceTime(300)).subscribe((searchText) => {
      this.doSearch();
    });

    if (
      this.roleUser === ROLE_ACCOUNT.SUPER_ADMIN ||
      this.roleUser === ROLE_ACCOUNT.ADMIN
    ) {
      this.listRole = [
        ...this.listRole,
        {
          label: 'Admin',
          code: ROLE_ACCOUNT.ADMIN,
        },
        {
          label: 'Partner',
          code: ROLE_ACCOUNT.PARTNER,
        },
      ];
    }
    // this.doSearch();

    this.initRoleOptions();
    this.initReferralOptions();
  }

  closeFilter() {
    this.viewFilter.set(false);
  }

  initRoleOptions() {
    this.roleOptions = [
      { label: 'All Roles', value: '' },
      { label: 'User', value: ROLE_ACCOUNT.USER },
      { label: 'Admin', value: ROLE_ACCOUNT.ADMIN },
      { label: 'Partner', value: ROLE_ACCOUNT.PARTNER },
    ];
  }

  initPlanOptions() {
    this.planOptions = [{ label: 'All Plans', value: '0' }];

    if (this.listPlan && this.listPlan.length > 0) {
      const planOptions = this.listPlan.map((plan: any) => ({
        label: plan?.name || 'Unknown Plan',
        value: plan?.id?.toString() || '0',
      }));

      this.planOptions = [{ label: 'All Plans', value: '0' }, ...planOptions];
    }
  }

  initReferralOptions() {
    this.referralOptions = [{ label: 'All Referrals', value: '' }];

    this.userManagementService.getListUserHaveReferral().subscribe({
      next: (users: any[]) => {
        if (users && users.length > 0) {
          const referralOptions = users.map((user) => ({
            label: `${user.first_name} ${user.last_name} (${user.email})`,
            value: user.email,
          }));

          this.referralOptions = [
            { label: 'All Referrals', value: '' },
            ...referralOptions,
          ];
          this.referralOptionsFilted = this.referralOptions;
        }
      },
      error: (error: any) => {
        console.error('Error loading referral options:', error);
      },
    });
  }

  getAllListPlan() {
    this.planService.getAllPlans({}, { page: 1, page_size: 999999 }).subscribe({
      next: (res) => {
        if (res.items && res.items.length > 0) {
          this.listPlan = res.items;
          this.listPlanSearch = res.items.map((item: any) => ({
            label: item.name,
            code: item.id,
          }));
          this.listPlanSearch.unshift({ label: 'All', code: 0 });

          this.initPlanOptions();
        } else {
          this.listPlanSearch = [
            {
              label: 'All',
              code: 0,
            },
          ];

          this.planOptions = [{ label: 'All Plans', value: '0' }];
        }
      },
      error: (err) => {
        console.error(err);
        this.planOptions = [{ label: 'All Plans', value: '0' }];
      },
    });
  }

  doSearch(event?: any) {
    if (event) {
      if (event.pageSize) {
        this.paramModel = {
          pageIndex: event.pageIndex,
          pageSize: event.pageSize,
        };
        this.searchModel.pageSize = event.pageSize;
      }
    }
    this.isLoading.set(true);
    if (this.paramModel.pageSize !== this.searchModel.pageSize) {
      this.paramModel.pageSize = this.searchModel.pageSize;
    }

    this.getListUser(this.searchModel, this.paramModel).subscribe(
      (items: IUserInfo[]) => {
        this.isLoading.set(false);
        this.listUser = items;
      }
    );
  }

  getListUser(body?: any, params?: any): Observable<IUserInfo[]> {
    let requestParams = {};
    if (params) {
      requestParams = {
        page: params.pageIndex + 1,
        page_size: params.pageSize,
      };
    }
    let requestBody = {};
    if (body) {
      requestBody = body;
    }

    return this.userManagementService
      .getListUser(requestBody, requestParams)
      .pipe(
        map((res: any) => {
          this.count = res.total;
          if (res.items && res.items.length !== 0) {
            return res.items.map((item: any) => ({
              ...item,
              fullName: item.first_name + ' ' + item.last_name,
              isDeleting: false,
            }));
          } else {
            return [];
          }
        })
      );
  }

  changePage(event: any) {
    this.paramModel = {
      pageIndex: event.pageIndex,
      pageSize: event.pageSize,
    };
    this.searchModel.pageSize = event.pageSize;
    this.updateUrlParams(); // <-- Thêm dòng này
    // this.doSearch();
  }

  changeFilter(): void {
    this.paramModel.pageIndex = 0;
    const currentPageSize =
      this.searchModel.pageSize || this.paramModel.pageSize || 20;
    this.searchModel.pageSize = currentPageSize;
    this.paramModel.pageSize = currentPageSize;
    this.updateUrlParams();  // <-- Thêm dòng này
    // this.searchSubject.next(this.searchModel.key_word);
    // this.doSearch();
  }

  showDeleteUser(user: any) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this user',
          content: 'Are you sure delete this user ?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value) {
          this.confirmDeleteUser(user);
        }
      });
  }

  confirmDeleteUser(data: any) {
    if (data.id) {
      const index = this.listUser.findIndex((item) => item.id === data.id);
      this.listUser[index] = {
        ...this.listUser[index],
        isDeleting: true,
      };
      this.listUser = cloneDeep(this.listUser);
      this.userManagementService.deleteUser(data.id).subscribe({
        next: (res: any) => {
          if (res) {
            this.paramModel.pageIndex = 0;
            this.showSnackBar('Delete user successfully!', 'success');
            this.doSearch();
          }
        },
        error: (err: any) => {
          this.listUser[index] = {
            ...this.listUser[index],
            isDeleting: false,
          };
          this.showSnackBar('Delete user failed!', 'error');
          this.listUser = cloneDeep(this.listUser);
        },
      });
    }
  }

  handleTimePlanTooltip(row: any, type: string) {
    let date = '';
    if (type === 'start_date') {
      if (row.start_date) {
        date = 'Start date: ' + moment(row.start_date).format('DD/MM/YYYY');
      } else {
        date = 'Start date: ' + 'None';
      }
    } else if (type === 'end_date') {
      if (row.end_date) {
        date = 'End date: ' + moment(row.end_date).format('DD/MM/YYYY');
      } else {
        date = 'End date: ' + 'None';
      }
    }
    return date;
  }

  handleNamePlan(name: string) {
    return name.includes('Yearly') || name.includes('Monthly')
      ? name.split(' ')[0]
      : name;
  }

  showChangePassword(template: any, row: any) {
    this.dialog.open(ChangePassDialogComponent, {
      data: {
        user_id: row.id,
      },
      width: '500px',
      minWidth: '340px',
    });
  }

  showChangeRole(row: any) {
    this.dialog
      .open(ConfirmRoleComponent, {
        width: '300px',
        data: {
          roles: this.roleOptions,
        },
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value) {
          const body = {
            id: row.id,
            role_new: value,
          };
          this.userManagementService.changeRoleUser(body).subscribe({
            next: () => {
              this.doSearch();
              this.showSnackBar('Change role successfully', 'success');
            },
            error: (err: any) => {
              this.showSnackBar(err.error.message, 'error');
            },
          });
        }
      });
  }

  handleClassesPlan(row: any) {
    if (row.includes(TYPE_PLAN.FREE)) {
      return 'border-light-plan-free text-light-plan-free hover:bg-light-plan-free hover:text-light-white';
    } else if (row.includes(TYPE_PLAN.START)) {
      return 'border-light-plan-starter text-light-plan-starter hover:bg-light-plan-starter hover:text-light-white';
    } else if (row.includes(TYPE_PLAN.ESSENTIAL)) {
      return 'border-light-plan-essential text-light-plan-essential hover:bg-light-plan-essential hover:text-light-white';
    } else if (row.includes(TYPE_PLAN.BUSINESS)) {
      return 'border-light-plan-business text-light-plan-business hover:bg-light-plan-business hover:text-light-white';
    } else {
      return '';
    }
  }

  changePlanBySuperAdmin(row: IUserInfo) {
    const listPlan = (this.listPlan || [])
      .filter(
        (item: any) => item.scope === row.email || item.scope === 'public'
      )
      .map((item: any) => ({
        value: item.id.toString(),
        label: item.name,
        disabled: item.id === row.plan_id,
      }));

    this.newSubscription = null;
    this.dialog
      .open(ChangeSubComponent, {
        data: {
          plan: row.plan,
          listPlanRow: listPlan,
          user_id: row.id,
        },
        width: '500px',
      })
      .afterClosed()
      .subscribe(() => this.doSearch());
  }

  resendEmail(row: any) {
    if (this.roleUser !== ROLE_ACCOUNT.SUPER_ADMIN) {
      return;
    }
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Resend email to activate account',
          content:
            "Are you sure to resend the email to activate this user's account ?",
        },
        width: '30vw',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value === 'success') {
          this.userManagementService
            .resendMailActivateAccount(row.id)
            .subscribe({
              next: (res: any) => {
                if (res) {
                  this.showSnackBar('Resend email successfully!', 'success');
                }
              },
            });
        }
      });
  }

  showRenewPeriod(row: any) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Renew plan usage period',
          content: `Are you sure to renew this user's plan usage period ?`,
        },
        width: '400px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value) {
          const body = {
            user_id: row.id,
            plan_id: row.plan_id,
          };
          this.userManagementService.renewPlanUsagePeriod(body).subscribe({
            next: () => {
              this.doSearch();
            },
          });
        }
      });
  }

  handleAction(caseName: string, row: any, dialog: any) {
    switch (caseName) {
      case 'renew':
        this.showRenewPeriod(row);
        break;
      case 'password':
        this.showChangePassword(dialog, row);
        break;
      case 'role':
        this.showChangeRole(row);
        break;
      case 'delete':
        this.showDeleteUser(row);
        break;
      case 'activate':
        this.showActivateUser(row);
        break;
    }
  }

  showAIOwner(user: any) {
    this.filterAI.user_id = user.id;
    this.filterAI.name = '';
    this.dialog.open(ListUserAisComponent, {
      data: {
        user: user,
      },
      width: '60dvw',
      minWidth: '340px',
    });
  }

  filterAIList() {
    this.settingsService.getListAIOwn(this.filterAI).subscribe((res) => {
      this.listAI = res;
    });
  }

  /*showDashboard(data: any) {
    const dashboardRef = super.showDialog(this.dashboardComponent, {
      data: {
        aiId: data.id,
      },
      width: '100vw',
      minWidth: '90vw',
      maxHeight: '90vh',
    });
  }*/

  /*openCreateFolderDialog() {
    this.createUserRef = super.showDialog(this.createUserDialog, {
      data: {},
      width: '40vw',
    });
  }*/

  openCreateUserDialog() {
    this.dialog
      .open(AddUserComponent, {
        data: {
          user_id: 0,
        },
        height: '58dvh',
        width: '40dvw',
      })
      .afterClosed()
      .subscribe(() => this.doSearch());
  }

  showActivateUser(user: any) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Activate this user',
          content: 'Are you sure activate this user ?',
          isDelete: false,
          confirmText: 'Activate',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value) {
          this.confirmActivateUser(user);
        }
      });
  }

  confirmActivateUser(data: any) {
    if (data.id) {
      const index = this.listUser.findIndex((item) => item.id === data.id);
      this.listUser[index] = {
        ...this.listUser[index],
        email_verified: true,
      };
      this.listUser = cloneDeep(this.listUser);
      this.userManagementService.activateUser(data.id).subscribe({
        next: (res: any) => {
          if (res) {
            this.paramModel.pageIndex = 0;
            this.doSearch();
          }
        },
        error: (err: any) => {
          this.listUser[index] = {
            ...this.listUser[index],
            isDeleting: false,
          };
          this.listUser = cloneDeep(this.listUser);
        },
      });
    }
  }

  acceptUpdatePlan(row: any): boolean {
    const currentUser = this.currentUser;
    if (currentUser.email === EMAIL_SUPER_ADMIN) {
      return true;
    }
    if (currentUser.role === ROLE_ACCOUNT.SUPER_ADMIN) {
      return true;
    }
    if (currentUser.role === ROLE_ACCOUNT.USER) {
      return false;
    }
    if (currentUser.role === ROLE_ACCOUNT.ADMIN) {
      return row.role !== ROLE_ACCOUNT.ADMIN;
    }
    if (currentUser.role === ROLE_ACCOUNT.PARTNER) {
      return (
        row.role !== ROLE_ACCOUNT.PARTNER && row.email !== currentUser.email
      );
    }
    return false;
  }

  private showSnackBar(message: string, type: 'success' | 'error') {
    if (type === 'success') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }

    if (type === 'error') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }

  onSearchReferralEmail() {
    this.referralOptionsFilted = this.referralOptions.filter((referral) =>
      referral.label
        .toLowerCase()
        .includes(this.searchReferralEmail.toLowerCase())
    );
  }

  updateUrlParams() {
    const queryParams: any = {};
    // Filter luôn có trên URL (dù rỗng), muốn gọn hơn thì chỉ set khi có giá trị thật
    queryParams['key_word'] = this.searchModel.key_word;
    queryParams['role'] = this.searchModel.role;
    queryParams['plan_id'] = this.searchModel.plan_id;
    queryParams['referral_email'] = this.searchModel.referral_email;
    // Phân trang
    queryParams['pageIndex'] = this.paramModel.pageIndex;
    queryParams['pageSize'] = this.paramModel.pageSize;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
      replaceUrl: true,
    });
  }
}
