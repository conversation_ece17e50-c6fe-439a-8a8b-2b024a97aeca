import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, computed, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  ConfirmDialogComponent,
  DataTableComponent,
  IColumn,
  MobileDrawerComponent,
  MobileHeaderComponent,
  SelectOption,
  SvgIconComponent,
} from '@shared/components';
import { ClickOutsideDirective } from '@shared/directives';
import { CapitalPipe } from '@shared/pipes';

import { Router } from '@angular/router';
import { APP_ROUTES, ROLE_ACCOUNT } from '@core/constants';
import { UIStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import {
  heroArrowPath,
  heroArrowTopRightOnSquare,
  heroCheckCircle,
  heroDocumentDuplicate,
  heroEllipsisHorizontal,
  heroTrash,
  heroXCircle,
} from '@ng-icons/heroicons/outline';
import { STATUS } from '@shared/app.constant';
import { PlanService } from '@shared/services';
import { AddOrEditPlanComponent } from '@views/admin/pages/list-plan/add-or-edit-plan/add-or-edit-plan.component';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { ActivatedRoute, Params } from '@angular/router';

@Component({
  selector: 'app-list-plan',
  standalone: true,
  imports: [
    CommonModule,
    DataTableComponent,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    NgIconsModule,
    OverlayModule,
    ClickOutsideDirective,
    CapitalPipe,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    DxButton,
    DxTooltip,
    DxPrefix,
    SvgIconComponent,
    MobileHeaderComponent,
    MobileDrawerComponent,
    DxLabel,
  ],
  templateUrl: './list-plan.component.html',
  styleUrl: './list-plan.component.css',
  providers: [
    provideIcons({
      heroEllipsisHorizontal,
      heroArrowPath,
      heroTrash,
      heroArrowTopRightOnSquare,
      heroCheckCircle,
      heroXCircle,
      heroDocumentDuplicate,
    }),
  ],
})
export class ListPlanComponent implements OnInit {
  formGroup: FormGroup = new FormGroup({});
  count: number = 0;
  columns: IColumn[] = [
    {
      columnDef: 'index',
      headerName: 'No.',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'name',
      headerName: 'Plan name',
      flex: 0.4,
      minWidth: '180px',
    },
    {
      columnDef: 'price',
      headerName: 'Price',
      flex: 0.2,
      minWidth: '80px',
    },
    {
      columnDef: 'duration',
      headerName: 'Duration',
      flex: 0.2,
      minWidth: '80px',
    },
    {
      columnDef: 'scope',
      headerName: 'Scope',
      flex: 0.4,
      minWidth: '180px',
    },
    /*{
      columnDef: 'created_by',
      headerName: 'Created By',
      flex: 0.2,
      minWidth: '80px',
    },*/
    {
      columnDef: 'status',
      headerName: 'Status',
      flex: 0.1,
      minWidth: '80px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'action',
      headerName: 'Action',
      flex: 0.1,
      minWidth: '120px',
      alignHeader: 'center',
      align: 'center',
    },
  ];
  listAllPlan: any[] = [];
  // Extend the base searchModel with list-plan specific properties
  searchModel: any = {
    key_word: '',
    duration: '',
    page: 0,
    pageSize: *********,
  };

  // Pagination options
  pageSizeOptions: number[] = [10, 20, 50, 100];

  // Math object for template use
  Math = Math;
  durationOptions: SelectOption[] = [
    {
      label: 'All',
      value: '',
    },
    {
      label: 'Monthly',
      value: 'monthly',
    },
    {
      label: 'Yearly',
      value: 'yearly',
    },
  ];

  listFeature: any[] = [];
  createOrUpdatePlanDialogRef: any;
  isEmailScopeValid = true;
  STATUS = STATUS;
  currentUser: any = { email: '', role: ROLE_ACCOUNT.ADMIN }; // This should be replaced with actual user data
  searchSubject = new Subject<string>();
  fb = inject(FormBuilder);
  dialog = inject(DxDialog);
  private route = inject(ActivatedRoute);
  private snackBar = inject(DxSnackBar);
  protected uiStore = inject(UIStore);
  protected isHandset = computed(() => this.uiStore.isHandset());
  private router = inject(Router);
  get formGroupFeature(): FormGroup {
    return this.formGroup.get('features') as FormGroup;
  }

  protected readonly ROLE_ACCOUNT = ROLE_ACCOUNT;
  viewFilter = signal<boolean>(false);
  isLoading = signal<boolean>(false);
  closeFn!: () => void;
  backFn = () => {
    this.router.navigate([APP_ROUTES.MENU]);
  };

  constructor(private planService: PlanService) {
    this.closeFn = this.closeFilter.bind(this);
  }

  ngOnInit() {
    this.route.queryParams.subscribe((params: Params) => {
      this.searchModel.key_word = params['key_word'] ?? '';
      this.searchModel.duration = params['duration'] ?? '';
      this.searchModel.page = params['page'] ? +params['page'] : 0;
      this.searchModel.pageSize = params['pageSize'] ? +params['pageSize'] : 999;
      this.doSearch();
    });
    this.searchSubject.pipe(debounceTime(300)).subscribe(() => this.doSearch());
  }
  closeFilter() {
    this.viewFilter.set(false);
  }
  doSearch() {
    this.getListPlan(this.searchModel, this.searchModel);
  }

  // Reset to first page when filters change
  changeFilter() {
    this.searchModel.page = 0;
    this.updateUrlParams();         // THÊM DÒNG NÀY
    // this.searchSubject.next(this.searchModel.key_word);
    // this.doSearch();
  }

  getListPlan(body: any, params: any) {
    this.isLoading.set(true);
    this.planService
      .getAllPlans(body, { page: params.page + 1, page_size: params.pageSize })
      .subscribe({
        next: (res) => {
          this.isLoading.set(false);
          if (res.items && res.items.length !== 0) {
            this.listAllPlan = res.items;
            this.count = res.total;
          } else {
            this.listAllPlan = [];
            this.count = res.total;
          }
        },
      });
  }

  /**
   * Handle pagination events from the data-table component
   * This is called when the user changes the page or page size
   */
  changePage(event: any) {
    this.searchModel.page = event.pageIndex;
    this.searchModel.pageSize = event.pageSize;
    this.updateUrlParams();        // THÊM DÒNG NÀY

    this.getListPlan(this.searchModel, this.searchModel);
  }

  // The getPageNumbers method is no longer needed as pagination is handled by the data-table component

  showCreatePlan() {
    this.handleValueFormGroupPlan();
    this.createOrUpdatePlanDialogRef = this.dialog.open(
      AddOrEditPlanComponent,
      {
        data: {
          isCreate: true,
        },
        height: '80dvh',
        width: '80dvw',
        maxWidth: '100dvw',
        minWidth: '340px',
        panelClass: 'large-dialog',
      }
    );
    this.createOrUpdatePlanDialogRef
      .afterClosed()
      .subscribe(() => this.doSearch());
  }

  showEditPlan(row: any) {
    /*this.handleValueFormGroupPlan(row);
    this.isEmailScopeValid = true;*/
    this.dialog
      .open(AddOrEditPlanComponent, {
        data: {
          isCreate: false,
          plan: row,
        },
        height: '80dvh',
        width: '80dvw',
        maxWidth: '100dvw',
        panelClass: 'large-dialog',
      })
      .afterClosed()
      .subscribe(() => this.doSearch());
  }

  handleValueFormGroupPlan(data?: any) {
    this.listFeature.forEach((feature) => {
      const valueObj = data?.features.find(
        (item: any) => item.feature.id === feature.id
      );
      const initialValue = valueObj ? valueObj.value : null; // Default value if not found
      this.formGroupFeature.get(`${feature.id}`)?.setValue(initialValue);
    });
    this.formGroup.patchValue({
      id: data?.id ?? null,
      name: data?.name ?? null,
      price: data?.price ?? 0,
      duration: data?.duration ?? 'monthly',
      currency: data?.currency ?? 'USD',
      stripe_payment_link: data?.stripe_payment_link ?? null,
      scope: data?.scope === 'public' ? 'public' : 'custom',
      valueScope: data?.scope === 'public' ? 'public' : data?.scope || '',
    });
  }

  showDeletePlan(user: any) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this plan',
          content: 'Are you sure delete this plan ?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value) => {
        if (value) {
          this.confirmDeletePlan(user);
        }
      });
  }

  confirmDeletePlan(data: any) {
    if (data.id) {
      this.planService.deletePlan(data.id).subscribe({
        next: (res) => {
          if (res) {
            this.showSnackBar(res.message, 'success');
            this.searchModel.page = 0;
            this.doSearch();
          }
        },
      });
    }
  }

  showClonePlan(row: any) {
    /*this.handleValueFormGroupPlan(row);
    if (this.currentUser.role == ROLE_ACCOUNT.ADMIN) {
      this.formGroup.patchValue({
        scope: 'public',
        valueScope: 'public',
      });
    }
    this.formGroup.get('name')?.setValue(this.formGroup.get('name')?.value + '-(Copy)');
    this.isEmailScopeValid = true;*/

    this.dialog
      .open(AddOrEditPlanComponent, {
        data: {
          isCreate: false,
          isClone: true,
          plan: row,
        },
        height: '80dvh',
        width: '80dvw',
        maxWidth: '100dvw',
        panelClass: 'large-dialog',
      })
      .afterClosed()
      .subscribe(() => this.doSearch());
  }

  onAction(event: any) {
    const { action, row } = event;
    switch (action) {
      case 'edit':
        this.showEditPlan(row);
        break;
      case 'delete':
        this.showDeletePlan(row);
        break;
      case 'clone':
        this.showClonePlan(row);
        break;
      default:
        break;
    }
  }

  // Helper methods for template
  hasQuantityFeatures(): boolean {
    return this.listFeature.some((item) => item.type === 'Quantity');
  }

  hasBooleanFeatures(): boolean {
    return this.listFeature.some((item) => item.type === 'Boolean');
  }

  showSnackBar(message: string, type: 'success' | 'error') {
    if (type === 'success') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }

    if (type === 'error') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }

  updateUrlParams() {
    const queryParams: any = {};
    queryParams['key_word'] = this.searchModel.key_word;
    queryParams['duration'] = this.searchModel.duration;
    queryParams['page'] = this.searchModel.page;
    queryParams['pageSize'] = this.searchModel.pageSize;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
      replaceUrl: true,
    });
  }

  closeMenu(row: any) {
    row.isActions = false;
    row.isContextMenu = false;
  }
}
