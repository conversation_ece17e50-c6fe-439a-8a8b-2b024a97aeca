@if (!uiStore.isHandset()) {
  <div class="h-full flex flex-col overflow-hidden">
    <div
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      Plan Management
    </div>
    <div
      class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
    >
      <div class="flex flex-wrap items-center justify-between">
        <div class="flex items-center space-x-4 flex-wrap">
          <dx-form-field
            class="w-full md:w-96"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.key_word"
              (ngModelChange)="changeFilter()"
              [type]="'text'"
              placeholder="Search by Name"
            />
          </dx-form-field>
          <!-- Duration Filter -->
          <dx-form-field
            class="w-full md:w-48"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <dx-select
              [(ngModel)]="searchModel.duration"
              (selectionChange)="changeFilter()"
            >
              @for (duration of durationOptions; track $index) {
                <dx-option [value]="duration.value">{{ duration.label }}</dx-option>
              }
            </dx-select>
          </dx-form-field>
        </div>
        <div class="flex items-center justify-end">
          <button dxButton (click)="showCreatePlan()">
            <div class="flex items-center justify-between space-x-1">
              <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
              <span class="text-sm">Add Plan</span>
            </div>
          </button>
        </div>
      </div>

      <div class="flex-1 flex overflow-hidden">
        <app-data-table
          class="w-full"
          [rows]="listAllPlan"
          [columns]="columns"
          [pageIndex]="searchModel.page"
          [limit]="searchModel.pageSize"
          [count]="count"
          [rowTemplate]="rowTemplate"
          [actionTemplate]="actionTemplate"
          [hiddenPaginator]="true"
          (pageChange)="changePage($event)"
          (action)="onAction($event)"
          [loading]="isLoading()"
        ></app-data-table>
      </div>
    </div>
  </div>
} @else {
  <app-mobile-header [title]="'Plan'" [backFn]="backFn"></app-mobile-header>
  <div
    class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
  >
    <div class="flex items-center justify-between space-x-3">
      <dx-form-field
        class="flex-1 w-full"
        [style.margin-bottom]="0"
        [style.--dx-form-field-label-offset-y]="0"
        [subscriptHidden]="true"
      >
        <app-svg-icon
          dxPrefix
          type="icSearch"
          class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
        ></app-svg-icon>
        <input
          dxInput
          [(ngModel)]="searchModel.key_word"
          (ngModelChange)="changeFilter()"
          [type]="'text'"
          placeholder="Search by Name"
        />
      </dx-form-field>
      <div class="flex-shrink-0 flex items-center space-x-1">
        <div
          class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
          (click)="showCreatePlan()"
        >
          <app-svg-icon
            type="icPlus"
            class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          ></app-svg-icon>
        </div>
        <div
          class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        >
          <app-svg-icon
            type="icFilter"
            class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
            (click)="viewFilter.set(true)"
          ></app-svg-icon>
        </div>
      </div>
    </div>
    <div
      class="bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border rounded-[12px]"
    >
      <div class="m-h-table">
        <app-data-table
          class="h-full"
          [rows]="listAllPlan"
          [columns]="columns"
          [pageIndex]="searchModel.page"
          [limit]="searchModel.pageSize"
          [count]="count"
          [rowTemplate]="rowTemplate"
          [actionTemplate]="actionTemplate"
          [hiddenPaginator]="true"
          (pageChange)="changePage($event)"
          (action)="onAction($event)"
          [loading]="isLoading()"
        ></app-data-table>
      </div>
    </div>
  </div>
  <app-mobile-drawer [visible]="viewFilter()">
    <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
      <app-mobile-header
        [title]="'Filter'"
        [backFn]="closeFn"
        [hideMenu]="true"
      ></app-mobile-header>
      <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
        <dx-form-field
          class="w-full md:w-48"
          [style.margin-bottom]="0"
          [subscriptHidden]="true"
        >
          <dx-label>Duration</dx-label>
          <dx-select
            [(ngModel)]="searchModel.duration"
            (selectionChange)="changeFilter()"
            placeholder="Duration"
          >
            @for (duration of durationOptions; track $index) {
              <dx-option [value]="duration.value">{{ duration.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>
    </div>
  </app-mobile-drawer>
}
<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
    @case ('status') {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        <div
          class="px-4 rounded-full text-center"
          [ngClass]="{
          'bg-success text-success-content': row[column.columnDef] === STATUS.ACTIVE,
          'bg-error text-error-content': row[column.columnDef] !== STATUS.ACTIVE
          }"
        >
          {{ row[column.columnDef] | titlecase }}
        </div>
      </div>
    }
    @case ('price') {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        {{
          (row[column.columnDef] | currency : "" : "" : "1.0-0") +
          " " +
          (row.currency ?? "USD")
        }}
      </div>
    }
    @case ('duration') {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        {{ row[column.columnDef] | capital }}
      </div>
    }
    @case ('scope') {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        {{ row[column.columnDef] | capital }}
      </div>
    }
    @default {
      <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
        {{ row[column.columnDef] }}
      </div>
    }
  }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <app-svg-icon
      type="icMoreHorizontal"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
      class="w-6 h-6 flex items-center justify-center cursor-pointer"
    ></app-svg-icon>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions && !row.isContextMenu"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetX: -5
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetX: -5
        }
      ]"
    >
      <ul
        class="action-dropdown w-[245px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 z-50"
        (clickOutside)="closeMenu(row)"
      >
        <ng-container *ngTemplateOutlet="actionMenu; context: { row: row }"></ng-container>
      </ul>
    </ng-template>

    <!-- MENU: Fixed tại vị trí chuột khi click phải -->
    <ul
      *ngIf="row.isActions && row.isContextMenu"
      class="fixed z-50 w-[245px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
      [style.left.px]="row.contextMenuX"
      [style.top.px]="row.contextMenuY"
      (clickOutside)="closeMenu(row)"
    >
      <ng-container *ngTemplateOutlet="actionMenu; context: { row: row }"></ng-container>
    </ul>
  </div>
</ng-template>

<ng-template #actionMenu let-row="row">
  <!-- Nếu user là admin thì có nút Duplicate -->
  <ng-container *ngIf="currentUser.role === ROLE_ACCOUNT.ADMIN">
    <li (click)="$event.stopPropagation(); showClonePlan(row); closeMenu(row)"
        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon dxTooltip="Duplicate plan" type="icCopy" class="w-6 h-6"></app-svg-icon>
      Duplicate
    </li>
  </ng-container>

  <!-- Nếu KHÔNG phải partner xem public thì có nút Edit -->
  @if (!(currentUser.role === ROLE_ACCOUNT.PARTNER && row.scope === 'public')) {
    <li (click)="$event.stopPropagation(); showEditPlan(row); closeMenu(row)"
        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon dxTooltip="Edit plan" type="icEdit" class="w-6 h-6 flex"></app-svg-icon>
      Edit
    </li>
    <!-- Nếu KHÔNG phải partner xem public VÀ status khác INACTIVE thì có nút Delete -->
    @if (row.status !== STATUS.INACTIVE) {
      <li (click)="$event.stopPropagation(); showDeletePlan(row); closeMenu(row)"
          class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon dxTooltip="Delete plan" type="icTrash" class="w-6 h-6"></app-svg-icon>
        Delete
      </li>
    }
  }
</ng-template>
