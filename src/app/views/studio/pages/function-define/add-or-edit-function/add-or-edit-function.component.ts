import { Component, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar,
} from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import { IFunction } from '@shared/models';
import { FunctionService } from '@shared/services';
import { CustomValidators } from '@shared/validators';

@Component({
  selector: 'app-add-or-edit-function',
  imports: [
    DxButton,
    DxError,
    DxFormField,
    DxInput,
    DxLabel,
    DxLoadingButton,
    FormsModule,
    ReactiveFormsModule,
    SvgIconComponent,
  ],
  templateUrl: './add-or-edit-function.component.html',
  styleUrl: './add-or-edit-function.component.css',
})
export class AddOrEditFunctionComponent implements OnInit {
  isSubmitting = signal<boolean>(false);
  dialogRef = inject(DxDialogRef<AddOrEditFunctionComponent>);
  data: {
    function: {
      id: number;
      ai_id: string;
      name: string;
      code: string;
    };
    isCreate: boolean;
  } = inject(DIALOG_DATA);
  snackBar = inject(DxSnackBar);
  formGroup = inject(FormBuilder).group({
    name: [
      '',
      [
        Validators.required,
        Validators.maxLength(255),
        CustomValidators.noWhitespaceValidator(),
      ],
    ],
  });
  private functionService: FunctionService = inject(FunctionService);

  ngOnInit() {
    if (!this.data.isCreate) {
      this.formGroup.patchValue({
        name: this.data.function.name,
      });
    }
  }

  saveFunction() {
    const body: IFunction = {
      ...this.data.function,
      name: this.formGroup.get('name')?.value ?? '',
    };
    this.isSubmitting.set(true);
    if (body.id) {
      this.functionService.update(body).subscribe({
        next: (res) => {
          this.isSubmitting.set(false);
          this.showSnackBar('Update successfully', 'success');
          this.dialogRef.close(res.id);
        },
        error: (err) => {
          this.isSubmitting.set(false);
        },
      });
    } else {
      this.functionService.insert({ ...body, id: null }).subscribe({
        next: (res) => {
          this.isSubmitting.set(false);
          this.showSnackBar('Create successfully', 'success');
          this.dialogRef.close(res.id);
        },
        error: (err) => {
          this.isSubmitting.set(false);
        },
      });
    }
  }

  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
