<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.isCreate ? "Create API" : "Edit API" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>
  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-y-4"
    >
      <div>
        <div
          class="text-xl font-bold text-base-content dark:text-dark-base-content mb-4"
        >
          Basic info
        </div>
        <dx-form-field class="w-full">
          <dx-label class="text-sm">Name</dx-label>
          <input dxInput formControlName="name" placeholder="API name" />
          @if ( formGroup.get('name')?.errors &&
          formGroup.get('name')?.errors?.['required'] &&
          (formGroup.get('name')?.touched || formGroup.get('name')?.dirty) ) {
          <dx-error>Name is required.</dx-error>
          }
        </dx-form-field>
        <dx-form-field class="w-full">
          <dx-label class="text-sm">Description</dx-label>
          <textarea
            dxInput
            autoResize
            rows="4"
            formControlName="description"
            placeholder="Description"
          ></textarea>
        </dx-form-field>
      </div>
      <div
        class="border-b border-primary-border dark:border-dark-primary-border"
      ></div>
      <div>
        <div
          class="text-xl font-bold text-base-content dark:text-dark-base-content mb-4"
        >
          Request information
        </div>
        <div class="flex space-x-4">
          <dx-form-field class="w-full flex-1">
            <dx-label class="text-sm">Method</dx-label>
            <dx-select formControlName="method">
              @for (method of REST_API_METHOD_LIST; track $index) {
              <dx-option [value]="method.value">
                {{ method.label }}
              </dx-option>
              }
            </dx-select>
          </dx-form-field>
          <dx-form-field class="w-full flex-2">
            <dx-label class="text-sm">Request URL</dx-label>
            <input dxInput formControlName="url" placeholder="Request URL" />
            @if ( formGroup.get('url')?.errors &&
            formGroup.get('url')?.errors?.['required'] &&
            (formGroup.get('url')?.touched || formGroup.get('url')?.dirty) ) {
            <dx-error>Request URL is required.</dx-error>
            }
          </dx-form-field>
        </div>
      </div>
    </div>
  </div>
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isSubmitting()"
      [disabled]="formGroup.invalid"
      (click)="saveApi()"
    >
      {{ !data.isCreate ? "Update" : "Create" }}
    </button>
  </div>
</div>
