import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {UIStore, UserAiStore} from '@core/stores';
import { DxTab, DxTabGroup } from '@dx-ui/ui';
import { ROLE } from '@shared/app.constant';
import { IAgentConfig, IAiConfig, IExportConfig } from '@shared/models';
import {
  AiConfigService,
  ExportConfigService,
  SettingsService,
} from '@shared/services';
import { StringUtils } from '@shared/utils';
import { AiSettingsComponent } from './components/ai-settings/ai-settings.component';
import { ExportSettingsComponent } from './components/export-settings/export-settings.component';
import { GatherInformationSettingsComponent } from './components/gather-information-settings/gather-information-settings.component';
import { HumanHandoffSettingsComponent } from './components/human-handoff-settings/human-handoff-settings.component';

import { SearchSettingsComponent } from './components/search-settings/search-settings.component';
import { UserSettingsComponent } from './components/user-settings/user-settings.component';
import { WidgetSettingsComponent } from './components/widget-settings/widget-settings.component';
import {DataTableComponent, MobileHeaderComponent} from '@shared/components';
import {APP_ROUTES} from '@core/constants';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    DxTabGroup,
    DxTab,
    AiSettingsComponent,
    GatherInformationSettingsComponent,
    HumanHandoffSettingsComponent,
    WidgetSettingsComponent,
    SearchSettingsComponent,
    ExportSettingsComponent,
    UserSettingsComponent,

    MobileHeaderComponent,
    DataTableComponent,
  ],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css'],
})
export class SettingsComponent implements OnInit {
  selectedTab = signal<number>(0);
  settingData = signal<IAgentConfig>({});
  aiConfigData = signal<IAiConfig | undefined>(undefined);
  exportConfigData = signal<IExportConfig[]>([]);

  readonly ROLE = ROLE;
  title = 'Settings';
  backFn = ()=>{};

  userAiStore = inject(UserAiStore);
  uiStore = inject(UIStore);
  private settingsService = inject(SettingsService);
  private aiConfigService = inject(AiConfigService);
  private exportConfigService = inject(ExportConfigService);
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);

  ngOnInit(): void {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      const tabSlug = params.get('tab');
      if (tabSlug) {
        const tabLabels = this.getTabLabels().map((label) =>
          StringUtils.slugify(label)
        );
        const index = tabLabels.indexOf(tabSlug);
        if (index > -1) {
          this.selectedTab.set(index);
        }
      }
    });

    this.settingsService.getDetailSetting().subscribe({
      next: (data: any) => this.settingData.set(data),
    });

    this.aiConfigService
      .getAiConfig({
        ai_id: this.userAiStore.currentAi()?.id,
      })
      .subscribe({
        next: (data: IAiConfig) => {
          if (data && Object.keys(data).length !== 0) {
            this.aiConfigData.set(data);
          } else {
            this.aiConfigData.set({
              ai_id: this.userAiStore.currentAiId() ?? '',
              threshold: 0.25,
              bot_style: '',
              show_source: true,
            });
          }
        },
      });

    this.exportConfigService.getExportConfig().subscribe({
      next: (data: IExportConfig[]) => this.exportConfigData.set(data),
    });
    this.backFn = () => {
      this.router.navigate([APP_ROUTES.MENU]);
    }
  }

  onTabChange(index: number) {
    this.selectedTab.set(index);
    const tabLabel = this.getTabLabels()[index];
    if (tabLabel) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { tab: StringUtils.slugify(tabLabel) },
        queryParamsHandling: 'merge',
      });
    }
  }

  private getTabLabels(): string[] {
    const labels = [
      'AI settings',
      'Human handoff',
      'Widget',
      'Search',
      'Export',
      'User',
    ];

    if (
      this.userAiStore.currentAi()?.role === ROLE.OWNER ||
      this.userAiStore.currentAi()?.role === ROLE.ADMIN
    ) {
      labels.splice(3, 0, 'LLM');
      labels.splice(4, 0, 'Gather information');
    }

    return labels;
  }
}
