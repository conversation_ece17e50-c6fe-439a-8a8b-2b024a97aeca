import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, input, signal } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  DxButton,
  DxCheckbox,
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxSelect,
  DxSlideToggle,
  DxSnackBar,
} from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import {
  IAgentConfig,
  ICustomParameter,
  IDefaultParameter,
} from '@shared/models';
import { SettingsService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-gather-information-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxFormField,
    DxLabel,
    DxInput,
    DxSelect,
    DxSlideToggle,
    DxButton,
    SvgIconComponent,
    DragDropModule,
    DxCheckbox,
    DxOption,
    FormsModule,
  ],
  templateUrl: './gather-information-settings.component.html',
})
export class GatherInformationSettingsComponent {
  settings = input<IAgentConfig>();

  aiModels = signal<any[]>([
    { value: 'gpt-4o-mini', type: 'openai', label: 'gpt-4o-mini' },
    { value: 'gpt-4o', type: 'openai', label: 'gpt-4o' },
    { value: 'gpt-4.1-mini', type: 'openai', label: 'gpt-4.1-mini' },
    { value: 'gpt-4.1-nano', type: 'openai', label: 'gpt-4.1-nano' },
    { value: 'gpt-4.1', type: 'openai', label: 'gpt-4.1' },
    { value: 'gemini-1.5-flash', type: 'gemini', label: 'gemini-1.5-flash' },
    { value: 'gemini-2.0-flash', type: 'gemini', label: 'gemini-2.0-flash' },
  ]);

  protected readonly paramTypes: { value: string; label: string }[] = [
    { value: 'string', label: 'String' },
    { value: 'number', label: 'Number' },
    { value: 'boolean', label: 'Boolean' },
    { value: 'array', label: 'Array' },
  ];
  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);

  // FORM CONTROL
  enabled = new FormControl<boolean>(false);
  ai_model = new FormControl<string>('gpt-4.1-mini');
  question_count = new FormControl<number>(1);
  default_parameters = new FormControl<IDefaultParameter[]>([]);
  parameters = new FormControl<ICustomParameter[]>([]);
  webhook_set_user_data = new FormControl<string>('');

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });
  }

  ngOnInit(): void {
    if (this.settings()) {
      this.initializeFormSubscriptions();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  onDragStart(event: DragEvent, index: number) {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', index.toString());
    }
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDrop(event: DragEvent, toIndex: number) {
    event.preventDefault();
    if (event.dataTransfer) {
      const fromIndex = parseInt(event.dataTransfer.getData('text/plain'));
      if (fromIndex !== toIndex) {
        this.onMoveParameter(fromIndex, toIndex);
      }
    }
  }
  drop(event: CdkDragDrop<ICustomParameter[] | null>) {
    if (this.parameters.value === null) return;
    if (event.previousIndex === event.currentIndex) return;
    this.onMoveParameter(event.previousIndex, event.currentIndex);
  }

  onMoveParameter(fromIndex: number, toIndex: number) {
    const params = this.parameters.value ? [...this.parameters.value] : [];
    if (
      fromIndex >= 0 &&
      fromIndex < params.length &&
      toIndex >= 0 &&
      toIndex < params.length
    ) {
      const [item] = params.splice(fromIndex, 1);
      params.splice(toIndex, 0, item);
      this.parameters.setValue(params);
    }
  }

  addParam() {
    const params = this.parameters.value ? [...this.parameters.value] : [];
    params.push({
      key: '',
      type: 'string',
      description: '',
      note: '',
      is_required: false,
    });
    this.parameters.setValue(params);
  }

  deleteParam(index: number) {
    const params = this.parameters.value ? [...this.parameters.value] : [];
    params.splice(index, 1);
    this.parameters.setValue(params);
  }

  changeDefaultParam(
    event: string,
    index: number,
    key: 'description' | 'note' | 'is_required'
  ) {
    const params = this.default_parameters.value
      ? [...this.default_parameters.value]
      : [];
    if (params[index]) {
      params[index] = { ...params[index], [key]: event };
      this.default_parameters.setValue(params);
    }
  }

  changeCustomParam(event: any, index: number, key: keyof ICustomParameter) {
    const params = this.parameters.value ? [...this.parameters.value] : [];
    if (params[index]) {
      params[index] = { ...params[index], [key]: event };
      this.parameters.setValue(params);
    }
  }

  private updateSettingsFormControls(settings: any) {
    const transformDefaultParameters = (params: any[]) => {
      const defaults = {
        user_name: {
          label: 'Name',
          type: 'string',
          description: 'Tên của khách hàng',
          note: 'Xin tên khách hàng để tiện xưng hô',
        },
        phone_number: {
          label: 'Phone',
          type: 'string',
          description:
            'Số điện thoại của người dùng, phục vụ cho mục đích liên lạc và trao đổi thông tin một cách thuận tiện',
          note: 'Nếu người dùng từ chối hoặc né tránh không cung cấp thì hãy luôn giải thích đây là thông tin cần thiết, hoặc gợi ý người dùng cung cấp một phương thức liên lạc khác như: email, facebook, ...',
        },
        email: {
          label: 'Email',
          type: 'string',
          description: 'Địa chỉ email người dùng',
          note: 'Định dạng hợp lệ hợp lệ (ví dụ: <EMAIL>)',
        },
      };

      return Object.keys(defaults).map((key) => {
        const serverParam = params?.find((p) => {
          const normalizedKey =
            p.key === 'name'
              ? 'user_name'
              : p.key === 'phone'
              ? 'phone_number'
              : p.key;
          return normalizedKey === key;
        });

        return {
          key,
          ...defaults[key as keyof typeof defaults],
          is_required: serverParam?.is_required || false,
        };
      });
    };

    this.enabled.setValue(
      settings?.settings?.agent_setting?.gather_information?.enabled,
      { emitEvent: false }
    );
    this.ai_model.setValue(
      settings?.settings?.agent_setting?.gather_information?.ai_model ??
        'gpt-4o-mini',
      { emitEvent: false }
    );
    this.question_count.setValue(
      settings?.settings?.agent_setting?.gather_information?.question_count ??
        1,
      { emitEvent: false }
    );
    this.default_parameters.setValue(
      transformDefaultParameters(
        settings?.settings?.agent_setting?.gather_information
          ?.default_parameters
      ),
      { emitEvent: false }
    );
    this.parameters.setValue(
      settings?.settings?.agent_setting?.gather_information?.parameters?.map(
        (param: any) => ({
          key: param.key || '',
          type: param.type || 'string',
          description: param.description || '',
          note: param.note || '',
          is_required: param.is_required || false,
        })
      ) ?? [],
      { emitEvent: false }
    );
    this.webhook_set_user_data.setValue(
      settings?.settings?.agent_setting?.gather_information
        ?.webhook_set_user_data,
      { emitEvent: false }
    );
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(500)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlSettings(this.enabled);
    this.subscribeToFormControlSettings(this.ai_model);
    this.subscribeToFormControlSettings(
      this.question_count,
      (value: number) => value >= 0
    );
    this.subscribeToFormControlSettings(this.default_parameters);
    this.subscribeToFormControlSettings(this.parameters);
    this.subscribeToFormControlSettings(this.webhook_set_user_data);
  }

  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      agent_setting: {
        ...currentSettings?.agent_setting,
        gather_information: {
          ...currentSettings?.agent_setting?.gather_information,
          enabled: this.enabled.value,
          ai_model: this.ai_model.value,
          question_count: this.question_count.value,
          default_parameters: this.default_parameters.value,
          parameters: this.parameters.value,
          webhook_set_user_data: this.webhook_set_user_data.value,
        },
      },
    };
    this.updateSettings(updatedSettings);
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }
}
