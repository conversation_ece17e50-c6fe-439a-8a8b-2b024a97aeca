@if (!isHandset()) {
<div class="h-full flex flex-col overflow-hidden">
  <div
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    Tag Management
  </div>
  <!-- Search and Filter Controls -->
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between gap-4">
      <div class="flex items-center gap-4 flex-wrap">
        <!-- Search Input -->
        <dx-form-field
          class="w-full lg:w-64"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="changeFilter()"
            [type]="'text'"
            placeholder="Search by Tag Name"
          />
        </dx-form-field>

        <dx-form-field
          class="w-full md:w-48"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="selectedApplication"
            (ngModelChange)="onApplicationChange()"
          >
            @for (type of applicationOptions; track $index) {
            <dx-option [value]="type.value">{{ type.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>

        <!-- Status Filter -->
        <dx-form-field
          class="w-full md:w-48"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="selectedStatus"
            (ngModelChange)="onStatusChange()"
          >
            @for (status of statusOptions; track $index) {
            <dx-option [value]="status.value">{{ status.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>

      <div class="flex items-center justify-end">
        <button dxButton="filled" (click)="createNewTag()" class="px-4 py-2">
          <div class="flex items-center justify-between space-x-1">
            <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
            <div class="text-sm font-medium">Add Tag</div>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        class="w-full"
        [rows]="listTag"
        [columns]="columns"
        [pageIndex]="pageIndex"
        [limit]="searchModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
        [loading]="isLoading()"
      ></app-data-table>
    </div>
  </div>
</div>
} @else {
<app-mobile-header [title]="title" [backFn]="backFn"></app-mobile-header>
<div
  class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div class="flex items-center justify-between space-x-3">
    <!--Search -->
    <dx-form-field
      class="w-full flex-1"
      [style.margin-bottom]="0"
      [style.--dx-form-field-label-offset-y]="0"
      [subscriptHidden]="true"
    >
      <app-svg-icon
        dxPrefix
        type="icSearch"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        [(ngModel)]="searchModel.key_word"
        (ngModelChange)="changeFilter()"
        [type]="'text'"
        placeholder="Search by Tag Name"
      />
    </dx-form-field>

    <div class="flex-shrink-0 flex items-center space-x-1">
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        (click)="createNewTag()"
      >
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        ></app-svg-icon>
      </div>
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
      >
        <app-svg-icon
          type="icFilter"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="viewFilter.set(true)"
        ></app-svg-icon>
      </div>
    </div>
  </div>
  <div
    class="bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border rounded-[12px]"
  >
    <div class="m-h-table">
      <app-data-table
        class="h-full"
        [rows]="listTag"
        [columns]="columns"
        [pageIndex]="pageIndex"
        [limit]="searchModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
        [loading]="isLoading()"
      ></app-data-table>
    </div>
  </div>
</div>
<app-mobile-drawer [visible]="viewFilter()">
  <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
    <app-mobile-header
      [title]="'Filter'"
      [backFn]="closeFn"
      [hideMenu]="true"
    ></app-mobile-header>
    <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
      <dx-form-field
        class="w-full"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Type</dx-label>
        <dx-select
          [(ngModel)]="selectedApplication"
          (ngModelChange)="onApplicationChange()"
        >
          @for (type of applicationOptions; track $index) {
          <dx-option [value]="type.value">{{ type.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>

      <!-- Status Filter -->
      <dx-form-field
        class="w-full"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Status</dx-label>
        <dx-select
          [(ngModel)]="selectedStatus"
          (ngModelChange)="onStatusChange()"
        >
          @for (status of statusOptions; track $index) {
          <dx-option [value]="status.value">{{ status.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>
</app-mobile-drawer>
}

<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
  <!-- Index column -->
  @case ('index') {
  <div class="flex items-center justify-center">
    <span>{{ getRowIndex(row) + 1 }}</span>
  </div>
  }
  <!-- Tag name column -->
  @case ('name') {
  <div
    class="!flex items-center truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
  >
    <div
      class="px-4 rounded-full tag"
      [ngStyle]="{
        'background-color': getColor(row.config),
        color: getTextColor(getColor(row.config))
      }"
      [dxTooltip]="row[column.columnDef]"
      dxTooltipPosition="below"
    >
      {{ row[column.columnDef] }}
    </div>
  </div>
  }
  <!-- Application type column -->
  @case ('application') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="
      row[column.columnDef] === 'FILE' ? 'Knowledge Base' : 'Conversation'
    "
    dxTooltipPosition="below"
  >
    @if (row[column.columnDef] === 'FILE') { Knowledge Base } @else {
    Conversation }
  </div>
  }
  <!-- Updated at column -->
  @case ('updated_at') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="getFormatDate(row[column.columnDef])"
    dxTooltipPosition="below"
  >
    {{
      row[column.columnDef]
        ? (row[column.columnDef] + "Z" | date : "dd/MM/yyyy HH:mm:ss")
        : ""
    }}
  </div>
  }
  <!-- Created by column -->
  @case ('created_by') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Default for any other columns -->
  @default {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <button
      class="flex cursor-pointer hover:opacity-80"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    >
      <ng-icon
        name="heroEllipsisHorizontal"
        size="24"
        class="flex items-center justify-center"
      ></ng-icon>
    </button>
    <ng-template
    *ngIf="row.isActions && !row.isContextMenu"
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="trigger"
    [cdkConnectedOverlayOpen]="row.isActions && !row.isContextMenu"
    [cdkConnectedOverlayPush]="true"
    [cdkConnectedOverlayPositions]="[
      { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'top', offsetY: 10 },
      { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'bottom', offsetY: 10 }
    ]"
  >
    <ul
      class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
      (clickOutside)="row.isActions = false; row.isContextMenu = false"
    >
      <ng-container *ngTemplateOutlet="tagMenu; context: { row: row }"></ng-container>
    </ul>
  </ng-template>
  <!-- Context menu khi click phải -->
  <ul
    *ngIf="row.isActions && row.isContextMenu"
    class="fixed z-50 w-[245px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    [style.left.px]="row.contextMenuX"
    [style.top.px]="row.contextMenuY"
    (clickOutside)="row.isActions = false; row.isContextMenu = false"
  >
    <ng-container *ngTemplateOutlet="tagMenu; context: { row: row }"></ng-container>
  </ul>
  </div>
</ng-template>

<ng-template #tagMenu let-row="row">
  <!-- Sửa tag -->
  <li>
    <button
      (click)="
        $event.stopPropagation();
        handleAction('edit', row);
        row.isActions = false; row.isContextMenu = false;
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
      title="Edit tag"
    >
      <app-svg-icon
        type="icEdit"
        class="w-6 h-6 flex items-center justify-center"
      ></app-svg-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        Edit
      </div>
    </button>
  </li>
  <!-- Xoá tag -->
  <li>
    <button
      (click)="
        $event.stopPropagation();
        handleAction('delete', row);
        row.isActions = false; row.isContextMenu = false;
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
      title="Delete tag"
    >
      <ng-icon
        name="heroTrash"
        class="text-2xl flex items-center justify-center"
      ></ng-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        Delete
      </div>
    </button>
  </li>
</ng-template>
